<!DOCTYPE html>
<html <?php language_attributes(); ?>>

<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title><?php wp_title('|', true, 'right'); ?></title>
    <?php wp_head(); ?>
    <style>
        /* Normal header styles */
        .smort-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0 auto;
            width: calc(100% - 40px);
            padding: 10px 20px;
        }


        div#burger-menu span:nth-of-type(2) {
            max-width: 35px;
        }


        .menu-main-container {
            font-family: 'CustomHeadingFont';
        }

        /* Sticky header styles */
        .sticky-header {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%) scale(0);
            width: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 999;
            padding: 10px 20px;
            transition: transform 0.2s ease-out, width 0.2s ease-out;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            opacity: 0;
            border-radius: 2px;
            <?php if (is_product()): ?>background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            <?php else: ?>background-color: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            <?php endif; ?>
        }

        .sticky-header.active {
            transform: translateX(-50%) scale(1);
            /* Grow from center */
            width: 95%;
            /* Expand to full width */
            opacity: 1;
            /* Make visible */
        }

        img.modal-logo-mobile {
            width: 100%;
            position: absolute;
            bottom: -25%;
            left: 0;
        }

        .sticky-header .logo {
            display: flex;
            align-items: center;
        }

        .sticky-header .burger-menu {
            display: flex;
            flex-direction: column;
            cursor: pointer;
            max-width: 50px;
            align-items: center;
        }

        .sticky-header .burger-menu span {
            height: 2px;
            width: 100%;
            margin: 2px 0;
            background: var(--accentColor);
        }



        .burger-menu.sticky-burger-menu span:nth-of-type(2) {
            width: 80%;
        }

        .right-sticky button {
            background: transparent;
            border-radius: 0;
            color: #fff;
            border: 0px;
            width: 200px;
            text-align: left;
            font-family: var(--fontFamily);
            padding: 12px 15px;
            position: relative;
            text-transform: uppercase;
        }

        .right-sticky button:after {
            content: url('/wp-content/themes/smort_commerce/img/arrow-up-right.svg');
            position: absolute;
            right: 10px;
            filter: invert(1);
            transition: transform 0.2s ease-in-out;
            top: 10px;
        }

        .right-sticky button:hover:after {
            transform: rotate(45deg);
        }

        .right-sticky {
            display: flex;
            gap: 30px;
            width: 350px;
            justify-content: flex-end;
        }

        /* Preserve your button and other content */
        .sticky-header .smort-cart {
            display: flex;
            align-items: center;
        }

        .smort-footer {
            background-color: <?php echo get_field('secondary_color', 'option'); ?>;
        }

        .smort-nav ul li a:hover,
        .smort-nav ul li a:active {
            color: #fff;
        }

        .smort-logo img {
            height: <?php echo get_field('logo_height', 'option'); ?>px;
        }

        <?php if (get_field('transparent_header', 'option')): ?>.smort-header {
            background: none;
            position: absolute;
            width: calc(100% - 2%);
            padding: 1%;
        }

        body {
            margin-top: 0;
            background-color: var(--backgroundColor);
        }

        <?php endif; ?><?php if (get_field('enable_topbar', 'option')): ?>.smort-topbar {
            background-color: <?php echo get_field('topbar_background_color', 'option'); ?>;
            text-align: center;
            padding: 10px;
            color: #fff;
        }

        <?php endif; ?>

        /* Mobile Menu Styles */
        .burger-menu-container {
            display: none;
            top: 0;
            left: 10px;
            width: 40%;
            display: flex;
            justify-content: flex-start !important;
            align-items: center;
        }

        .burger-menu {
            display: flex;
            flex-direction: column;
            cursor: pointer;
            padding: 0px;
            max-width: 50px;
            position: initial;
        }

        .mobile-nav {
            display: block;
            position: fixed;
            top: 0;
            left: -100%;
            width: 40%;
            height: 100%;
            background: #101017;
            justify-content: space-between;
            align-items: flex-start;
            flex-direction: column;
            transition: left 0.3s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.3s ease;
            pointer-events: none;
            margin-top: 0px;
            z-index: 999999;
            opacity: 0;
            will-change: left, opacity;
        }

        /* Overlay för fade-in bakgrund */
        .mobile-nav-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999998;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
            backdrop-filter: blur(3px);
            -webkit-backdrop-filter: blur(3px);
            z-index: 999;
        }

        .mobile-nav-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .mobile-nav.active {
            left: 0;
            pointer-events: auto;
            opacity: 1;
        }

        .mobile-nav-inner li a {
            font-size: 2.2rem !important;
            line-height: 1.2;
            text-transform: uppercase;
            font-family: 'CustomHeadingFont';
        }

        .mobile-nav-inner li {
            text-align: left;
        }

        .mobile-nav-inner {
            text-align: center;
            width: calc(100% - 40px);
            overflow-y: auto;
            /* Scroll men ingen scrollbar synlig */
            text-align: left;
            padding-top: 20%;
            margin: 20px;

            /* Döljer scrollbaren för Webkit-baserade webbläsare (Chrome, Safari, Edge) */
            -webkit-overflow-scrolling: touch;
            /* För smidig scroll på iOS */
        }

        .mobile-nav-inner::-webkit-scrollbar {
            display: none;
            /* Döljer scrollbaren */
        }

        /* Döljer scrollbar för Firefox */
        .mobile-nav-inner {
            scrollbar-width: none;
            /* Firefox */
        }

        /* Edge och IE-specifika lösningar */
        .mobile-nav-inner {
            -ms-overflow-style: none;
            /* Internet Explorer 10+ */
        }


        .close-nav {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            font-size: 30px;
            color: #000;
            cursor: pointer;
            background: var(--accentColor);
            padding: 10px 20px;
            border-radius: 50%;
        }

        .mobile-nav ul {
            list-style-type: none;
            padding: 0;
        }

        ul.sub-menu li {
            border-bottom: 0px !important;
            margin: 20px 0px !important;
        }

        .mobile-nav ul li {
            margin: 30px 0;
            border-bottom: 1px solid #5a5a5a;
            padding: 10px;
        }

        .mobile-nav ul li a {
            color: #fff;
            text-decoration: none;
            font-size: 20px;
        }

        .mobile-nav ul li ul {
            display: none;
            list-style-type: none;
            padding: 0;
        }

        .mobile-nav ul li.active>ul {
            display: block;
        }

        @media (max-width: 992px) {

            .burger-menu-container,
            .smort-logo,
            .smort-cart {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                width: 33%;
                display: flex;
                align-items: center;
                position: relative;
                height: 50px;
            }

            .burger-menu-container,
            .smort-header-style_2 .smort-cart {
                width: 20% !important;
            }

            .main-navigation {
                display: none;
            }

            .smort-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: calc(100% - 0px);
                padding: 0px;
            }

            div#burger-menu span:nth-of-type(2) {
                width: 70%;
            }

            .burger-menu {
                align-items: flex-start;
            }

            .mobile-nav {
                justify-content: center;
                direction: ltr;
                padding-top: 4%;
                width: 100%;
                justify-content: space-between;
            }

            .mobile-nav-inner li a {
                font-size: 1.2rem !important;
            }

            .burger-menu {
                z-index: 10 !important;
            }

            .menu-menu-1-container ul {
                text-align: left;
            }

            .contact-overlaymenu {
                padding-left: 5px !important;
            }

            ul.sub-menu a {
                font-size: 1.6rem !important;
            }

            .right-sticky button {
                display: none;
            }

            .right-sticky {
                justify-content: end;
            }

            .sticky-header img {
                height: 20px !important;
            }

            button#wc_search_trigger {
                display: none;
            }

            .sticky-header.active {
                width: 85%;
            }

            .smort-logo img {
                height: 60px;
                padding-left: 10px;
            }

            ul.sub-menu.open {
                margin-left: 20px;
                margin-top: 20px;
                margin-bottom: 20px;
            }

            .darksoul-button1 {
                display: none;
            }

            .contact-overlaymenu a {
                width: 100px !important;
                font-size: 12px;
            }

            h3.cta-mobile-menu {
                width: 100%;
                color: #fff;
                font-size: 1.5rem !important;
                margin: 0px 0px 1px;
                padding-left: 15px;
            }

            .contact-overlaymenu {
                margin-bottom: 0px !important;
            }

            .mobile-nav ul li {
                margin: 20px 0px;
            }

            .mobile-nav ul li a {
                font-size: 20px !important;
            }
        }

        nav#mobile-nav .logo-inner {
            position: absolute;
            width: 140px;
            padding: 20px;
            top: 15px;
        }

        .mobile-nav ul li {
            opacity: 0;
            transform: translateY(10px);
            animation: fadeInUp 0.5s ease forwards;
            animation-delay: var(--delay, 0s);
        }

        /* Animation for fading and sliding in */
        @keyframes fadeInUp {
            0% {
                opacity: 0;
                transform: translateY(10px);
            }

            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Apply a delay to each menu item */
        .mobile-nav.active ul li:nth-child(1) {
            --delay: 0.1s;
        }

        .mobile-nav.active ul li:nth-child(2) {
            --delay: 0.2s;
        }

        .mobile-nav.active ul li:nth-child(3) {
            --delay: 0.3s;
        }

        .mobile-nav.active ul li:nth-child(4) {
            --delay: 0.4s;
        }

        .mobile-nav.active ul li:nth-child(5) {
            --delay: 0.5s;
        }


        .smort-pristabell-features-extra li {
            position: relative;
            padding-left: 25px;
            background: url(/wp-content/uploads/2024/11/check-9.svg) no-repeat left center;
            background-size: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .forklaring-content {
            display: none;
            margin: 5px;
            padding: 0px 10px;
            border: 1px solid #828282;
            border-radius: 10px;
        }

        span.forklaring-toggle {
            position: absolute;
            right: 20px;
            cursor: pointer;
        }

        .forklaring-content p {
            font-size: 14px;
        }

        .mobile-nav-inner .menu-menu-1-container ul {
            border: 0px;
            text-align: left;
        }

        .menu-item-has-children>.menu-arrow {
            margin-left: 10px;
            cursor: pointer;
            display: inline-block;
        }

        .mobile-menu .sub-menu {
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .mobile-menu .sub-menu.open {
            display: block;
            opacity: 1;
        }

        img.menu-arrow {
            width: 30px !important;
            transform: rotate(-45deg);
            filter: brightness(0) invert(1);
            position: absolute;
            right: 10px;
        }

        .menu-arrow {
            margin-left: 10px;
            vertical-align: middle;
            /* Aligns the image with the text */
        }

        .mobile-nav-inner ul.sub-menu a {
            font-size: 2.5rem;
        }

        ul.sub-menu a {
            font-size: 30px !important;
        }

        .contact-overlaymenu a {
            color: #fff;
            text-decoration: none;
            border: 1px solid #353535 !important;
            padding: 15px 30px;
            border-radius: 30px;
            font-family: var(--fontFamily);
            width: 150px;
            text-align: left;
            font-weight: 400;
            position: relative;
        }

        .contact-overlaymenu a:after {
            content: url(/wp-content/themes/smort_commerce/img/arrow-up-right.svg);
            position: absolute;
            right: 13px;
            top: 14px;
            transition: transform 0.2s ease-in-out;
            filter: invert(1);
        }

        h3.cta-mobile-menu {
            width: 100%;
            color: #fff;
            font-size: 2.5rem;
            margin: 0px 0px 15px;
        }

        .contact-overlaymenu {
            margin-top: 15px;
            padding-left: 0px;
            margin-bottom: 50px;
            display: flex;
            gap: 20px;
            width: calc(100% - 40px);
            padding: 20px;
            flex-wrap: wrap;
        }

        .mobile-nav-logo {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100vw;
            height: auto;
            max-height: 40vh;
            /* Display only the top 60% of the image */
            overflow: hidden;
            z-index: 1001;
            /* Ensure it's above other elements */
            transform: translateY(40%);
            /* Show only the top 60% */
        }

        .mobile-nav-logo img {
            width: 100%;
            /* Make the image fill the width of the viewport */
            height: auto;
        }

        .right-sticky .wooj-icon-basket-1:before {
            content: url(/wp-content/themes/smort_commerce/assets/smort-shopping-icon.svg) !important;
            filter: invert(0);
        }
    </style>


    <script>
        /*Sticky */

        document.addEventListener('DOMContentLoaded', function() {
            const header = document.querySelector('.smort-header');
            const stickyHeader = document.createElement('div');
            const screenWidth = window.innerWidth || document.documentElement.clientWidth;

            // Skapa overlay för fade-in effekt
            const mobileNavOverlay = document.createElement('div');
            mobileNavOverlay.classList.add('mobile-nav-overlay');
            document.body.appendChild(mobileNavOverlay);

            // Only create the sticky header for desktop devices
            stickyHeader.classList.add('sticky-header');
            stickyHeader.innerHTML = `
            <div class="logo">
                <a href="<?php echo esc_url(home_url('/')); ?>">
                    <?php $logo = get_field('logo', 'option'); ?>
                    <?php if ($logo): ?>
                        <img src="<?php echo esc_url($logo); ?>" alt="<?php bloginfo('name'); ?>" style="height: 40px;">
                    <?php else: ?>
                        Smort
                    <?php endif; ?>
                </a>
            </div>
			<div class="right-sticky">
				<button class="darksoul-button1" type="button" id="serviceButton">
                    <a href="/become-a-reseller" class="become-reseller-link">Become a reseller</a>
                </button>

				<div class="burger-menu sticky-burger-menu">
					<span></span>
					<span></span>
					<span></span>
				</div>
			</div>
        `;
            document.body.appendChild(stickyHeader);

            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) { // Adjust as needed for when the sticky header should appear
                    stickyHeader.classList.add('active');
                } else {
                    stickyHeader.classList.remove('active');
                }
            });

            const burgerMenu = document.getElementById('burger-menu');
            const stickyBurgerMenu = document.querySelector('.sticky-header .burger-menu');
            const mobileNav = document.getElementById('mobile-nav');
            const closeNav = document.getElementById('close-nav');

            // Open the mobile nav when the burger menu or sticky burger menu is clicked
            [burgerMenu, stickyBurgerMenu].forEach(menu => {
                if (menu) {
                    menu.addEventListener('click', function(e) {
                        e.preventDefault();
                        // Aktivera overlay först för fade-in effekt
                        mobileNavOverlay.classList.add('active');
                        // Kort fördröjning för att synkronisera animationerna
                        setTimeout(() => {
                            mobileNav.classList.add('active');
                        }, 50);
                        // Förhindra scroll på body när menyn är öppen
                        document.body.style.overflow = 'hidden';
                    });
                }
            });

            // Close the mobile nav when the close button is clicked
            closeNav.addEventListener('click', function() {
                mobileNav.classList.remove('active');
                setTimeout(() => {
                    mobileNavOverlay.classList.remove('active');
                }, 300); // Vänta tills menyn har glidit ut
                document.body.style.overflow = ''; // Återställ scroll
            });

            // Close the mobile nav when clicking on the overlay
            mobileNavOverlay.addEventListener('click', function() {
                mobileNav.classList.remove('active');
                setTimeout(() => {
                    mobileNavOverlay.classList.remove('active');
                }, 300);
                document.body.style.overflow = '';
            });
        });



        window.addEventListener('scroll', function() {
            const element = document.getElementById('smort-colorchange');
            const rect = element.getBoundingClientRect();
            const elementHeight = element.offsetHeight;
            const windowHeight = window.innerHeight || document.documentElement.clientHeight;

            // Calculate when 20% of the element is in view
            const triggerInPoint = windowHeight - elementHeight * 0.4;
            const triggerOutPoint = elementHeight * 0.2;

            // Check if 20% of the div is in view to add the new color
            if (rect.top <= triggerInPoint && rect.bottom >= triggerOutPoint) {
                element.classList.add('new-color');
            }
            // Check if less than 20% of the div is visible to revert to the default color
            else {
                element.classList.remove('new-color');
            }
        });





        /* Infinity scroll smort */
    </script>
</head>

<body <?php body_class(get_field('transparent_header', 'option') ? 'transparent-header' : ''); ?>>
    <?php if (get_field('enable_topbar', 'option')): ?>
        <div class="smort-topbar">
            NEXT GEN PREMIUM LIGHTS
            </span>
        </div>
    <?php endif; ?>
    <header class="smort-header smort-header-<?php echo get_field('header_style', 'option'); ?>">
        <?php if (get_field('header_style', 'option') == 'style_1'): ?>
            <div class="burger-menu-container">
                <div class="burger-menu" id="burger-menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
            <div class="smort-logo">
                <a href="<?php echo esc_url(home_url('/')); ?>">
                    <?php $logo = get_field('logo', 'option'); ?>
                    <?php if ($logo): ?>
                        <img src="<?php echo esc_url($logo); ?>" alt="<?php bloginfo('name'); ?>">
                    <?php else: ?>
                        Smort
                    <?php endif; ?>
                </a>
            </div>

            <div class="smort-cart">
                <?php echo do_shortcode('[wc_custom_search]'); ?>
                <a href="/my-account"><img src="/wp-content/uploads/2024/11/user-5.svg"></a>
                <?php echo do_shortcode('[woo_j_cart_count]'); ?>
            </div>
        <?php elseif (get_field('header_style', 'option') == 'style_2'): ?>
            <div class="smort-logo">
                <a href="<?php echo esc_url(home_url('/')); ?>">
                    <?php $logo = get_field('logo', 'option'); ?>
                    <?php if ($logo): ?>
                        <img src="<?php echo esc_url($logo); ?>" alt="<?php bloginfo('name'); ?>">
                    <?php else: ?>
                        Smort
                    <?php endif; ?>
                </a>
            </div>
            <div class="smort-cart">
                <nav class="main-navigation smort-nav smort-nav-centered">
                    <?php wp_nav_menu(array('theme_location' => 'main-menu')); ?>
                </nav>
                <div class="burger-menu-div">
                    <div class="burger-menu" id="burger-menu">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Mobile Menu Elements -->
        <nav class="mobile-nav" id="mobile-nav">
            <button class="close-nav" id="close-nav">&times;</button>
            <img class="logo-inner" src="<?php echo esc_url($logo); ?>" alt="<?php bloginfo('name'); ?>">
            <div class="mobile-nav-inner">
                <?php
                wp_nav_menu(array(
                    'theme_location' => 'mobile-menu',
                    'menu_class'     => 'mobile-menu',
                    'container'      => false,
                ));
                ?>
            </div>
        </nav>

    </header>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const filterButtons = document.querySelectorAll('.filter-button');
            const caseStudyItems = document.querySelectorAll('.case-study-item');

            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const category = button.getAttribute('data-category');

                    // Remove active class from all buttons and add to clicked button
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');

                    // Show/hide case study items
                    caseStudyItems.forEach(item => {
                        if (category === 'all') {
                            item.style.display = 'block';
                        } else {
                            if (item.classList.contains(category)) {
                                item.style.display = 'block';
                            } else {
                                item.style.display = 'none';
                            }
                        }
                    });
                });
            });
        });


        document.addEventListener('DOMContentLoaded', function() {
            const menuItems = document.querySelectorAll('.mobile-menu .menu-item-has-children > a');

            menuItems.forEach(function(item) {
                // Check if the arrow already exists
                if (!item.parentElement.querySelector('.menu-arrow')) {
                    // Create the arrow image element
                    const arrow = document.createElement('img');
                    arrow.classList.add('menu-arrow');
                    arrow.src = '/wp-content/uploads/2024/12/arrow-right-17.svg'; // Path to your image
                    arrow.alt = 'Expand menu'; // Optional alt text
                    arrow.style.width = '16px'; // Adjust size as needed
                    arrow.style.cursor = 'pointer'; // Make it clickable
                    arrow.style.transition = 'transform 0.3s ease'; // Add transition for rotation

                    // Append the image inside the link but positioned after the text
                    item.style.display = 'inline-flex';
                    item.style.alignItems = 'center';
                    item.appendChild(arrow);

                    // Add click event for the arrow
                    arrow.addEventListener('click', function(e) {
                        e.preventDefault();
                        const subMenu = item.nextElementSibling;

                        if (subMenu) {
                            subMenu.classList.toggle('open');
                            if (subMenu.classList.contains('open')) {
                                subMenu.style.display = 'block';
                                subMenu.style.opacity = '1';
                                subMenu.style.transition = 'opacity 0.3s';
                                arrow.style.transform = 'rotate(90deg)'; // Rotate the arrow
                            } else {
                                subMenu.style.opacity = '0';
                                setTimeout(() => {
                                    subMenu.style.display = 'none';
                                }, 300);
                                arrow.style.transform = 'rotate(0deg)'; // Reset rotation
                            }
                        }
                    });
                }
            });
        });


        document.addEventListener("DOMContentLoaded", function() {
            var items = document.querySelectorAll('.addons-item');

            items.forEach(function(item) {
                var header = item.querySelector('.addons-header');
                header.addEventListener('click', function() {
                    // Om objektet är öppet, stäng det
                    if (item.classList.contains('open')) {
                        item.classList.remove('open');
                    } else {
                        // Stäng alla andra accordeoner
                        items.forEach(function(i) {
                            i.classList.remove('open');
                        });

                        // Öppna den klickade accordion
                        item.classList.add('open');
                    }
                });
            });
        });



        /* Script E-handelsförfrågan */





        document.addEventListener('DOMContentLoaded', function() {
            const animatedElements = document.querySelectorAll('[data-animation="fade-in"]');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        observer.unobserve(entry.target); // Sluta observera när animationen är klar
                    }
                });
            }, {
                threshold: 0.1 // 10% av elementet måste vara synligt för att trigga animationen
            });

            animatedElements.forEach(element => {
                observer.observe(element);
            });
        });


        document.addEventListener("DOMContentLoaded", () => {
            const textContent = document.querySelector(".text-content");
            const toggleButton = document.getElementById("toggle-text");

            // Kolla längden av textinnehållet
            if (textContent.scrollHeight <= 200) {
                toggleButton.style.display = "none"; // Dölj knappen om texten är kort
            }

            toggleButton.addEventListener("click", () => {
                textContent.classList.toggle("expanded");
                if (textContent.classList.contains("expanded")) {
                    toggleButton.textContent = "Läs mindre";
                } else {
                    toggleButton.textContent = "Läs mer";
                }
            });
        });


        document.addEventListener('click', function(e) {
            // Kontrollera om den klickade knappen har klassen 'toggle-spec-btn'
            if (e.target && e.target.classList.contains('toggle-spec-btn')) {
                const toggleButton = e.target; // Själva knappen
                const productMetaData = toggleButton.nextElementSibling.querySelector('.product-meta-data'); // Nästa element med rätt klass

                if (!productMetaData) {
                    console.warn('Element saknas, kontrollera klasserna.');
                    return;
                }

                // Kontrollera elementets synlighet
                const isHidden = window.getComputedStyle(productMetaData).display === 'none';

                if (isHidden) {
                    productMetaData.style.display = 'block'; // Visa elementet
                    toggleButton.textContent = 'Dölj specifikation';
                } else {
                    productMetaData.style.display = 'none'; // Dölj elementet
                    toggleButton.textContent = 'Visa fullständig specifikation';
                }
            }
        });



        document.addEventListener('DOMContentLoaded', function() {
            const flagOptions = document.querySelectorAll('.flag-option');
            const hiddenCountryInput = document.getElementById('selected_country');

            if (flagOptions && hiddenCountryInput) {
                flagOptions.forEach(flag => {
                    flag.addEventListener('click', function() {
                        // Ta bort markerad klass från alla flaggor
                        flagOptions.forEach(option => option.classList.remove('selected'));

                        // Markera den klickade flaggan
                        this.classList.add('selected');

                        // Uppdatera värdet i hidden input
                        const countryValue = this.getAttribute('data-country');
                        hiddenCountryInput.value = countryValue;

                        console.log('Valt land:', countryValue);
                    });
                });
            }
        });


        document.addEventListener('DOMContentLoaded', function() {
            const cartElement = document.querySelector('.smort-cart');
            if (cartElement) {
                setTimeout(() => {
                    cartElement.classList.add('animate-in');
                }, 300);
            }
        });
    </script>

    <?php
    // Anpassad meny med bilder som bakgrund
    function smort_custom_menu($items, $args)
    {
        foreach ($items as &$item) {
            // Hämta bild-URL från metadata
            $image_url = get_post_meta($item->ID, '_menu_image_url', true);
            $image_enabled = get_post_meta($item->ID, '_menu_image_enabled', true);

            if ($image_enabled && $image_url) {
                // Lägg till en ny CSS-klass och inline-stil för bakgrundsbild
                $item->classes[] = 'menu-with-image';
                $item->title = '<span class="menu-overlay">' . esc_html($item->title) . '</span>';
                $item->description = '<div class="menu-image" style="background-image: url(' . esc_url($image_url) . ');"></div>';
            }
        }
        return $items;
    }
    add_filter('wp_nav_menu_objects', 'smort_custom_menu', 10, 2);

    ?>

</body>

</html>