.product-columns-wrapper {
  display: flex;
  flex-wrap: nowrap;
  gap: 5px;
}

.product-image-gallery {
  flex: 0 0 60%;
  display: flex;
  flex-direction: column;
  padding: 20px; /* Added padding */
  box-sizing: border-box; /* Ensure padding does not affect width */
  background-color: #f9f9f9;
  max-height: 1000px;
  overflow-y: scroll;
  scrollbar-width: none; /* Hide scrollbar for Firefox */
  -ms-overflow-style: none; /* Hide scrollbar for IE/Edge */
}

.product-image-gallery::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Chrome, Safari, and Opera */
}

.main-image img {
  width: 100%;
  border-radius: 10px;
  min-height: 700px;
  object-fit: contain;
  border: 1px solid #e2e2e2;
  background-color: #fff;
}

.gallery-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.gallery-grid .half-width {
  flex: 1 1 calc(50% - 10px);
}

.gallery-grid .full-width {
  flex: 1 1 100%;
}

.gallery-grid img {
  width: 100%;
  border-radius: 10px;
  height: 450px;
  object-fit: cover;
}

.product-summary {
  flex: 0 0 40%;
  display: flex;
  flex-direction: column;
  padding-left: 2%;
  padding-right: 2%;
  box-sizing: border-box;
  max-height: none;
  overflow-y: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
  background-color: #f9f9f9;
  justify-content: center;
}
.woocommerce div.product form.cart .button {
  vertical-align: middle;
  float: left;
  width: calc(100% - 10px);
  background-color: var(--accentColor);
  padding: 15px 0px;
  font-weight: 500;
  font-size: 1.5rem;
  border-radius: 0px;
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
  height: 50px;
  margin-left: -5px;
}
.woocommerce div.product form.cart {
  display: flex;
  margin-bottom: 10px;
}
.woocommerce div.product .product_title {
  margin-bottom: 0px;
  font-size: 3rem;
}

@media (max-width: 768px) {
  .woocommerce div.product .product_title {
    font-size: 2rem;
  }
}

.woocommerce-product-details__short-description {
  font-size: 16px !important;
  color: #5b5b5b;
  line-height: 1.7 !important;
}

.woocommerce-product-details__short-description p {
  font-size: 16px;
}

.woocommerce:where(body:not(.woocommerce-uses-block-theme))
  div.product
  p.price {
  margin: 10px 0px;
  font-family: "CustomHeadingFont";
  font-size: 2.2rem;
  color: var(--accentColor3);
  text-transform: uppercase;
  line-height: 1.5;
}

.yith-wapo-block .yith-wapo-addon.wapo-toggle .wapo-addon-title,
.cykel-modeller h3 {
  font-size: 2rem !important;
  margin-bottom: 15px;
  margin-top: 15px;
}
.yith-wapo-block .yith-wapo-addon.wapo-toggle .options.default-open {
  display: flex;
  flex-wrap: wrap;
}
tr.wapo-total-options,
tr.wapo-product-price {
  display: none;
}

/* Styling modeller */

.cykel-modeller {
  margin-top: 20px;
}

.cykel-modeller h3 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #333;
}

.cykel-modeller-list {
  list-style: none;
  padding: 0;
  display: flex;
  gap: 10px;
}

.cykel-modell-item {
  background-color: transparent;
  padding: 10px 30px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
  font-size: 1.5rem;
  line-height: 2;
}

.cykel-modell-item a {
  text-decoration: none;
  color: #000;
  font-family: "Arial";
}

.cykel-modell-item.current-model {
  background-color: transparent;
  color: #fff;
  font-weight: bold;
  pointer-events: none;
  border: 2px solid #000;
}

/* Quantity input */

.woocommerce div.product form.cart div.quantity {
  display: flex;
  height: calc(50px - 2px);
  border: 1px solid;
}
button.plus,
button.minus {
  background-color: transparent;
  border: 0px;
  font-size: 24px;
  padding: 0px 10px;
}
.woocommerce .quantity .qty {
  width: 40px;
  text-align: center;
  font-family: "CustomHeadingFont" !important;
  font-size: 1.4rem;
  border: 0px;
}

/* Slider Container */
.produkt-slider-container {
  position: relative;
  width: 100%;
  max-width: 100vw;
  margin: 0 auto;
  overflow: hidden;
  background: #000;
}

/* Slider */
.produkt-slider {
  display: flex;
  transition: transform 0.5s ease-in-out;
}

.produkt-slide {
  min-width: 100%;
  box-sizing: border-box;
  position: relative;
}

.produkt-slide img {
  width: 100%;
  display: block;
  max-height: 880px;
  object-fit: cover;
}

/* Navigationspilar - Placeras i nedre högra hörnet */
.slider-nav {
  position: absolute;
  bottom: 20px; /* Höjd för att matcha indikatorn */
  right: 20px; /* Höger hörn */
  background-color: transparent;
  color: #fff;
  border: none;
  border: 2px solid var(--accentColor);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 18px;
  cursor: pointer;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background 0.3s ease;
  margin-left: 10px; /* Avstånd mellan pilarna */
}

.slider-nav:hover {
  background-color: var(--accentColor);
  color: #000;
}

/* Justera pilar för att ligga jämte varandra */
.slider-nav.prev {
  right: 70px; /* Flytta vänsterpil bredvid högerpil */
}

/* Bildindikator */
.slider-indicator {
  position: absolute;
  bottom: 40px;
  left: 40px;
  width: 120px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  overflow: hidden;
  border-radius: 2px;
}

.indicator-progress {
  width: 0;
  height: 100%;
  background-color: #fff;
  transition: width 0.5s ease-in-out;
}

/* Responsiv fix */

/* Grundläggande responsiv styling */
/* Grundläggande styling för desktop */
.product-columns-wrapper {
  display: flex;
  gap: 0px;
}

.product-image-gallery {
  flex: 0 0 55%; /* Galleri 60% */
}

.product-summary {
  flex: 0 0 45%; /* Summary 40% */
}

/* Anpassningar för mobil */
@media (max-width: 768px) {
  .product-columns-wrapper {
    flex-direction: column; /* Lägg under varandra */
    gap: 0;
  }

  .product-image-gallery,
  .product-summary {
    flex: 1 1 100%; /* Full bredd */
  }

  /* Dölj det vanliga galleriet i mobil */
  .gallery-grid,
  .main-image {
    display: none;
  }

  /* Mobilslidern syns bara i mobil */
  .mobile-slider {
    display: block;
    position: relative;
    overflow: hidden;
    width: 100%;
  }

  .mobile-slider img {
    width: 100%;
    display: block;
    border-radius: 10px;
    object-fit: contain;
  }

  .slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 10;
  }

  .slider-nav.prev {
    left: 10px;
  }

  .slider-nav.next {
    right: 10px;
  }
  h2.tabs-main-title {
    font-size: 2.5rem !important;
  }
  .woocommerce .products ul,
  .woocommerce ul.products {
    flex-direction: column;
  }
  .woocommerce ul.products li.product,
  .woocommerce-page ul.products li.product {
    width: calc(100% - 20px) !important;
  }
}

@media (min-width: 769px) {
  .mobile-slider {
    display: none; /* Dölj slidern i desktop */
  }
}

/* Sidledes scroll för mobil */
@media (max-width: 768px) {
  .custom-tabs {
    display: flex;
    overflow-x: auto; /* Möjliggör sid-scroll */
    white-space: nowrap; /* Håll tabbarna på en rad */
    -webkit-overflow-scrolling: touch; /* Smidig scroll för iOS */
    gap: 10px; /* Litet mellanrum mellan tabbarna */
    padding-bottom: 5px; /* Extra spacing under tabbarna */
  }

  .custom-tabs::-webkit-scrollbar {
    display: none; /* Dölj scrollbar i Chrome/Safari */
  }

  .tab-button {
    flex: 0 0 45%;
    background-color: transparent;
    border: 0px;
    border-bottom: 2px solid rgb(181, 181, 181);
    padding: 7px 0px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    font-family: "Arial";
    white-space: normal;
    font-size: 0.5rem;
  }

  .tab-button.active {
    border-bottom: 2px solid #000; /* Aktiv tabb får svart markering */
    font-weight: bold;
  }
  .product-summary {
    max-height: none !important;
    padding-top: 1.5rem;
  }
}

/* Styling för desktop */
@media (min-width: 769px) {
  .tab-button {
    width: 25%; /* Fyra tabbar delar på bredden i desktop */
    text-align: left;
  }
}

/* Tabs produkter  */

/* Tabbar */
.custom-tabs-wrapper {
  padding: 2rem;
  background-color: #fff;
}

@media (max-width: 768px) {
  .custom-tabs-wrapper {
    padding: 0.8rem;
  }
}

.custom-tabs {
  display: flex;
  justify-content: flex-start;
  gap: 20px;
  margin-bottom: 20px;
}

.tab-button {
  background-color: transparent;
  border: 0px;
  border-bottom: 2px solid rgb(181, 181, 181);
  padding: 15px 0px;
  font-size: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 25%;
  text-align: left;
  font-size: 1.5rem;
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
}

@media (max-width: 768px) {
  .tab-button {
    font-size: 1.1rem;
  }
}

.tab-button.active {
  border-color: black;
  border-bottom: 3px solid #000;
}

.tab-content {
  display: none;
  animation: fadeIn 0.5s ease;
}

.tab-content.active {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  flex-direction: column;
}

.tab-section {
  display: flex;
  gap: 30px;
  align-items: center;
}

.tab-text {
  flex: 1;
  font-size: 1.2rem;
  line-height: 1.6;
}

.tab-text h2 {
  margin-bottom: 10px;
  font-size: 2rem;
}

.tab-image {
  flex: 1;
  text-align: center;
}

.tab-image img {
  max-width: 100%;
  border-radius: 0px;
  object-fit: cover;
}
h2.tabs-main-title {
  font-size: 4.5rem;
  line-height: 1;
  margin: 30px 0px;
  font-weight: 800;
}

/* Tenisk spec */

.product_meta {
  margin-top: 2%;
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
}

.posted_in {
  display: none;
}

/* Accordion styling */
.accordion {
  overflow: hidden;
}

.accordion-header {
  padding: 15px;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  user-select: none;
  margin-top: 4rem;
  border-top: 1px solid var(--accentColor2);
  border-bottom: 1px solid var(--accentColor2);
}

.accordion-toggle {
  background: none;
  border: none;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
}

.accordion-content {
  padding: 15px;
  display: block; /* Standard: öppen på desktop */
}

@media (max-width: 768px) {
  .accordion-content {
    display: none; /* Stängd som standard på mobil */
  }
}

/* Styling för tabellen */
.product_attributes {
  width: 100%;
  border-collapse: collapse;
}

.product_attributes th {
  font-weight: bold;
  text-align: left;
  padding: 10px 0;
  width: 50%;
}

.product_attributes td {
  text-align: right;
  padding: 10px 0;
  width: 50%;
}

.product_attributes tr {
  border-bottom: 1px solid #000;
}

.product_attributes tr:last-child {
  border-bottom: none;
}

.accordion-header span {
  font-size: 1.5rem;
}

/* Stock colors */

p.stock.in-stock:before {
  content: " ";
  width: 20px;
  height: 20px;
  background-color: #69d405 !important;
  display: inline-block;
  margin-right: 10px;
  border-radius: 50%;
}
.woocommerce div.product p.stock {
  font-size: 0.92em;
  display: flex;
  align-items: center;
  color: #000;
}

/* Huvudcontainer */
.hotspot-section-container {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  justify-content: center;
  padding: 5rem 2rem;
}

/* Bild-Container */
.hotspot-container {
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.half-width {
  flex: 0 0 calc(48% - 10px); /* 50% bredd med mellanrum */
}

/* Hotspot Punkt */
.hotspot {
  position: absolute;
  transform: translate(-50%, -50%);
  cursor: pointer;
}

/* Hotspot Punkt */
.hotspot-dot {
  position: relative;
  width: 30px;
  height: 30px;
  background-color: var(--accentColor); /* Accentfärg */
  border: 2px solid var(--accentColor); /* Vit border */
  color: #000;
  border-radius: 50%; /* Rund form */
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px; /* Storlek för plustecknet */
  font-weight: bold;
  text-align: center;
  animation: pulse 1.5s infinite; /* Pulseringsanimation */
}

.hotspot-dot::before {
  content: "+"; /* Plustecknet */
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(217, 229, 54, 0.6);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(217, 229, 54, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(217, 229, 54, 0);
  }
}

.hotspot:hover .hotspot-dot {
  transform: scale(1.3);
}

/* Tooltip Info */
.hotspot-info {
  display: none;
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 8px 10px;
  font-size: 1.5rem;
  border-radius: 4px;
  white-space: nowrap;
  text-transform: uppercase;
  font-family: "CustomHeadingFont";
}

.hotspot:hover .hotspot-info {
  display: block;
}

/* Responsiv Anpassning */
@media (max-width: 768px) {
  .half-width {
    flex: 0 0 100%; /* Bilder staplas vertikalt på mindre skärmar */
  }
  .hotspot-container.half-width img {
    max-height: 350px !important;
    object-fit: cover;
    min-height: 350px !important;
  }
}

.hotspot-container.half-width img {
  max-height: 600px;
  object-fit: cover;
  min-height: 600px;
}

/* Fade-in Effekt - Grundläggande styling */
.fade-in {
  opacity: 0; /* Start med att dölja sektionen */
  transform: translateY(20px); /* Flytta ner sektionen något */
  transition: opacity 0.8s ease-out, transform 0.8s ease-out; /* Tidsstyrd animation */
}

/* När sektionen är synlig */
.fade-in.show {
  opacity: 1; /* Full synlighet */
  transform: translateY(0); /* Flytta tillbaka till sin position */
}

/* Tabs styling inner */

.wysiwyg-content {
  line-height: 1.7;
  width: 100%;
}
.wysiwyg-content ul {
  list-style: none;
  padding: 0px;
  line-height: 2;
}

.tab-content h2 {
  font-size: 2rem;
}

@media (max-width: 768px) {
  .tab-content h2 {
    font-size: 1.5rem;
  }

  .tab-content p {
    font-size: 1rem;
  }
}

.las-mer-summary,
.las-mer-btn {
  background-color: transparent;
  border-radius: 0px;
  text-align: left;
  padding: 7px;
  min-width: 140px;
  border: 1px solid #000000;
  position: relative;
  font-family: var(--fontFamily);
  color: #000;
  text-transform: uppercase;
  width: 140px;
  margin: 15px 0px;
}

.las-mer-summary:after,
.las-mer-btn:after {
  content: var(--arrowRight);
  position: absolute;
  right: 4px;
  top: 4px;
}
.las-mer-summary:hover {
  transform: scale(1.03);
  transition: transform 0.2s ease-in-out;
}
.las-mer-summary:hover:after {
  transform: rotate(45deg);
  transition: transform 0.2s ease-in-out;
}
.wysiwyg-content li {
  border-bottom: 1px solid #dbdbdb;
  line-height: 2.5;
}

/* video styling */

/* Popup är dold från start */
.video-popup {
  display: none; /* Popup är nu dold */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  justify-content: center;
  align-items: center;
}

/* Visa popupen när den aktiveras */
.video-popup.active {
  display: flex;
}

/* Popup innehåll */
.video-popup-content {
  background: #000;
  padding: 0px;
  width: 90%;
  max-width: 800px;
  position: relative;
  border-radius: 10px;
}

/* Stäng-knappen */
.close-popup {
  position: absolute;
  top: 10px;
  right: 20px;
  font-size: 30px;
  color: white;
  cursor: pointer;
}

/* Video Thumbnail */
.video-thumbnail-container-sp {
  position: relative;
  max-width: 1000px !important;
  max-width: 100%;
  cursor: pointer;
  overflow: hidden;
  border-radius: 10px;
  margin: 3% 0%;
  margin: 50px auto;
}
.video-thumbnail-container-sp .video-play-button {
  top: 50%;
  width: 100px;
  height: 100px;
  font-size: 12px;
}

.video-thumbnail {
  width: 100%;
  height: auto;
  display: block;
  object-fit: cover;
  transform: scale(1.05);
  max-height: 700px;
  object-position: top;
}

.video-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: var(--accentColor);
  color: #fff;
  font-size: 18px;
  padding: 15px 30px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s ease;
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
  font-size: 2rem;
}

.video-play-button:hover {
  background-color: rgba(0, 0, 0, 0.9);
}

/* Container för innehållet */
.wysiwyg-content-container {
  position: relative;
  max-height: 450px; /* Begränsad höjd */
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

/* Innehållet */
.wysiwyg-content {
  padding-bottom: 20px;
}

/* Fade-effekt längst ner */
.fade-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, white 100%);
  transition: opacity 0.3s ease-in-out;
}

/* Dold fade-effekt när texten är utvikt */
.wysiwyg-content-container.expanded .fade-overlay {
  opacity: 0;
}

/* "Läs mer"-knappen */

/* Visa knappen endast om texten är längre än 250px */
.wysiwyg-content-container.overflow .las-mer-btn {
  display: block;
}

.related-products-section .products {
  display: flex;
}

.custom-tabs-wrapper.related-products-section {
  margin-top: 0px;
  margin-bottom: 0px;
}
.related-div-outer {
  display: flex;
  padding-bottom: 5%;
  padding-top: 5%;
  background: linear-gradient(to top, #030102 50%, #ffffff 50%);
}
.related-div-outer .product-info h2,
.related-div-outer .product-info span,
.related-div-outer .stock-status {
  color: #fff;
}

/* Produktförfrågan Popup */
.popup-overlay form {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

@media (max-width: 768px) {
  .popup-overlay form {
    padding: 5px;
  }
}

.popup-overlay form label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #fff;
  font-size: 14px;
  text-align: start;
}

.popup-overlay form input[type="text"],
.popup-overlay form input[type="email"],
.popup-overlay form textarea {
  width: 95%;
  padding: 14px 15px;
  margin-bottom: 15px;
  font-size: 14px;
  background-color: var(--accentColor2);
  box-shadow: none;
  border: none;
  color: #fff;
  font-family: "CustomHeadingFont";
}

@media (max-width: 768px) {
  .popup-overlay form input[type="text"],
  .popup-overlay form input[type="email"],
  .popup-overlay form textarea {
    width: 95%;
    padding: 10px;
    margin-bottom: 10px;
    font-size: 14px;
  }
}

.popup-overlay form input[type="text"]:focus,
.popup-overlay form input[type="email"]:focus,
.popup-overlay form textarea:focus {
  border-color: var(--accentColor);
  outline: none;
}

.popup-overlay form textarea {
  resize: none;
  height: 120px;
}

.popup-overlay form input[type="submit"] {
  background-color: transparent;
  border: 1px solid var(--accentColor);
  color: #fff;
  padding: 15px 20px;
  cursor: pointer;
  font-size: 16px;
  text-transform: uppercase;
  width: 100%;
}

.popup-overlay form input[type="submit"]:hover {
  background-color: var(--accentColor);
  color: #000;
}

.popup-overlay .wpcf7-spinner {
  display: none;
}

/* Popup-overlay */
.popup-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  justify-content: center;
  align-items: center;
}

/* Popup-innehåll */
.popup-content {
  background: var(--backgroundColor);
  padding: 25px 30px;
  width: 90%;
  max-width: 600px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  color: #fff;
  font-size: 25px;
  position: relative;
}

/* Stäng-knapp */
.close-popup {
  position: absolute;
  top: 10px;
  right: 20px;
  font-size: 30px;
  cursor: pointer;
  color: var(--accentColor);
}

/* Popup-knapp */
.popup-button {
  background-color: var(--accentColor);
  color: #000;
  padding: 16px 20px;
  border: none;
  cursor: pointer;
  font-size: 16px;
  text-transform: uppercase;
  width: 100%;
  margin-top: 1.5rem;
}

input::placeholder,
textarea::placeholder {
  color: #747789; /* valfri färg */
}

/* === Produktkarusell === */
.product-carousel {
  padding: 0;
  overflow: hidden;
}

.swiper-carousel-wrapper {
  position: relative;
  padding-top: 60px;
  /* Skapa plats för navigationen ovanför */
  overflow: hidden;
}

/* === Navigationspilar - flyttad till top right === */
.pagination-wrapper {
  position: absolute;
  top: -50px;
  right: 4rem;
  display: flex;
  gap: 10px;
  z-index: 10;
}

@media (max-width: 768px) {
  .pagination-wrapper {
    bottom: -38rem;
    right: 50%;
  }
}

.swiper-button-next,
.swiper-button-prev {
  color: var(--accentColor);
  border: 1px solid var(--accentColor);
  border-radius: 0;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
  background: var(--accentColor);
  color: #fff;
  border-color: var(--accentColor);
}

.swiper-button-next:after,
.swiper-button-prev:after {
  font-size: 15px;
}

/* === Produktkort === */
.product-slide {
  overflow: visible;
  border-radius: 0;
}

.product-slide img {
  width: calc(100% - 20px);
  height: 350px;
  object-fit: contain;
  margin: 10px;
  background-color: #161821;
  border-radius: 0 !important;
}

.product-slide img.placeholder {
  object-position: bottom !important;
}

/* === Swiperstruktur === */
.swiper-slide {
  height: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.swiper-wrapper {
  display: flex;
  align-items: stretch;
}

/* === Produktinfo === */
.related-slider-title {
  font-size: 1.7rem !important;
  color: #fff;
  max-width: 100%;
  margin: 0;
  line-height: 1.5;
}

.swiper-slide span.price bdi {
  font-size: 19px;
  text-transform: uppercase;
  font-family: "CustomHeadingFont";
}

.swiper-slide span.price ins {
  text-decoration: none;
}

/* === Sale badge === */
span.onsale {
  position: absolute;
  top: 20px;
  right: 20px;
  color: #fff;
  background-color: var(--accentColor);
  border-radius: 30px;
  padding: 10px;
  font-size: 15px;
}

/* === CTA & footer === */
.cta-div {
  padding-bottom: 20px;
  padding-left: 15px;
  margin-top: 10px;
}

a.related-slider-cta {
  text-align: center !important;
  display: block;
  background-color: var(--accentColor);
  color: #fff;
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
  padding: 10px;
  width: calc(100% - 30px);
}

.related-slider-cta img {
  width: 50px;
  height: 50px;
  object-fit: contain;
  margin: 0;
  border: 0 !important;
}

img.cta-arrow {
  width: 20px;
  height: 20px;
  margin: 5px 0 0;
}

/* === Swiper pagination === */
.swiper-pagination {
  margin-top: 15px;
  text-align: center;
  bottom: -30px !important;
}

.swiper-pagination-bullet {
  background: #333;
  opacity: 0.7;
}

.swiper-pagination-bullet-active {
  background: #4c8077;
  opacity: 1;
}

.swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer;
  width: 20px;
  border-radius: 5px;
}

.swiper-button-prev::after {
  content: "\2192";
  /* ← */
  font-size: 18px;
}

.swiper-button-next::after {
  content: "\2190";
  /* → */
  font-size: 18px;
}

p.related-slider-text {
  font-size: 14px;
  color: #fff;
  line-height: 1.7;
  margin-top: 5px;
}

.related-attribute-slider {
  font-size: 14px !important;
  line-height: 1.5;
  color: #fff;
  text-transform: uppercase;
  margin-bottom: 3px;
}

.related-slider-title {
  font-size: 1.5rem;
  color: #fff;
  margin-top: 5px;
  margin-bottom: 10px;
}

.related-slider-title {
  font-size: 3rem;
  color: #fff;
  margin-bottom: 0;
}

.related-slider-maintitle {
  font-size: 3rem;
  color: #fff;
  margin-top: 3rem;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .related-slider-maintitle {
    font-size: 2.5rem;
  }
}

.related-slider-container {
  padding: 0 40px;
  overflow: hidden;
}
@media (max-width: 768px) {
  .related-slider-container {
    padding: 0 20px;
  }
}

.slider-content-container {
  padding: 0 14px;
}

.related-slide img {
  width: calc(100% - 20px);
  height: 300px;
  object-fit: contain;
  margin: 10px;
  border-radius: 0 !important;
  background: var(--accentColor2);
}
