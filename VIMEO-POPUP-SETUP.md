# Vimeo Popup Setup Guide

## Overview
Your Vimeo widget has been converted from a regular block to a popup dialog system. The popup appears when buttons with the class `vimeo-popup-trigger` are clicked.

## What Was Done

### 1. Footer Integration
- Added popup HTML structure to `footer.php`
- The popup is now always present in the footer but hidden by default
- Added JavaScript to handle opening/closing the popup

### 2. Styling
- Created `assets/css/vimeo-popup.css` with responsive modal styles
- Added smooth animations and transitions
- Styled close button and overlay

### 3. ACF Options
- Created ACF options page for theme settings
- Added field for "Popup Vimeo Video ID" in WordPress admin
- Located at: **WordPress Admin > Theme Options**

### 4. JavaScript Functionality
- Popup opens when any element with class `vimeo-popup-trigger` is clicked
- Closes with close button, clicking outside, or pressing Escape key
- Stops video playback when closed
- Prevents background scrolling when open

## How to Use

### Step 1: Set the Video ID
1. Go to **WordPress Admin > Theme Options**
2. Find "Popup Vimeo Video ID" field
3. Enter your Vimeo video ID (just the numbers, e.g., "123456789")
4. Save the options

### Step 2: Add Trigger Buttons
Add buttons anywhere in your theme with the class `vimeo-popup-trigger`:

```html
<!-- Simple button -->
<button class="vimeo-popup-trigger">Watch Video</button>

<!-- Link button -->
<a href="#" class="vimeo-popup-trigger">Play Video</a>

<!-- Custom styled button -->
<button class="vimeo-popup-trigger custom-style">
    <i class="fas fa-play"></i> Watch Our Story
</button>
```

### Step 3: Examples
Check `template-parts/vimeo-popup-trigger-example.php` for more examples and styling ideas.

## Files Modified/Created

### Modified Files:
- `footer.php` - Added popup HTML and JavaScript
- `functions.php` - Added CSS enqueue and ACF options

### New Files:
- `assets/css/vimeo-popup.css` - Popup styling
- `template-parts/acf-options/vimeo-popup-options.php` - ACF field definitions
- `template-parts/vimeo-popup-trigger-example.php` - Usage examples
- `VIMEO-POPUP-SETUP.md` - This guide

## Customization

### Styling the Popup
Edit `assets/css/vimeo-popup.css` to customize:
- Popup size and positioning
- Background overlay color
- Animation effects
- Close button styling

### Styling Trigger Buttons
Add your own CSS classes to style the trigger buttons however you want. The only requirement is that they have the class `vimeo-popup-trigger`.

### JavaScript Customization
The popup JavaScript is in `footer.php`. You can modify it to:
- Change animation timing
- Add custom events
- Modify close behavior

## Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile responsive
- Touch-friendly on mobile devices

## Troubleshooting

### Popup Not Opening
1. Check that buttons have the class `vimeo-popup-trigger`
2. Verify video ID is set in Theme Options
3. Check browser console for JavaScript errors

### Video Not Loading
1. Verify the Vimeo video ID is correct
2. Ensure the video is publicly accessible
3. Check that the video exists on Vimeo

### Styling Issues
1. Clear any caching plugins
2. Check that `vimeo-popup.css` is loading
3. Verify CSS isn't being overridden by other styles

## Next Steps
1. Set your video ID in Theme Options
2. Add trigger buttons where you want them
3. Test the functionality
4. Customize styling as needed
