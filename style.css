/*
 Theme Name:   Smort Commerce Child
 Theme URI:    https://smort.se
 Description:  Smort Commerce Child Theme
 Author:       Smort AB
 Author URI:   https://smort.se
 Template:     smort_commerce
 Version:      1.0.0
*/

/* Import the parent theme's stylesheet */
@import url("../Smort_commerce/style.css");

/* Add your custom styles here */

/* Main */

/* Core */
:root {
  --textColor: #616573;
  --textColorLight: #fff;
  --textColorDark: rgb(136, 137, 145);
  --textColorHeadlines: var(--accentColor);

  --textSize: 18px;
  --textLineHeight: 160%;

  --fontFamily: "CustomHeadingFont", sans-serif;
  --fontFamilySecond: "Saira" !important;

  --arrowRight: url("/wp-content/themes/smort_commerce/img/arrow-up-right.svg");

  --accentColor: #f1ec47;
  --accentColor2: #1c1e28;
  --accentColor3: #272a37;
  --accentColor4: #f2f2f5;
  --backgroundColor: #101017;

  --buttonColorLight: #fff;
  --buttonColorDark: var(--accentColor2);
  --buttonTextLight: var(--textColorLight);
  --buttonTextDark: var(--textColorLight);

  --mainLetterSpacing: 2px;
}
article {
  margin-bottom: 0px;
}
body {
  font-family: "Helvetica";
}
p {
  font-size: 20px;
  line-height: 1.7;
}
a:hover {
  color: var(--accentColor2);
}
a:active {
  color: var(--accentColor) !important;
}
a {
  text-decoration: none;
}
html {
  scroll-behavior: smooth;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  text-transform: uppercase;
}
/* Header */

.smort-header {
  background: #fff;
  width: 100%;
  z-index: 9999;
}
.smort-header .menu-item a {
  color: #fff;
}

/* Headings */

.hero-heading-lx {
  font-size: 9rem;
  margin: 0px;
  line-height: 1;
  margin-bottom: 30px;
}
.main-heading-lx {
  font-size: 4.5rem;
  margin: 0px;
  margin-bottom: 0px !important;
}
.sub-heading-lx {
  font-size: 20px;
  text-transform: uppercase;
  margin-bottom: 10px;
}

@media (max-width: 992px) {
  .hero-heading-lx {
    font-size: 5rem;
  }
  .main-heading-lx {
    font-size: 2.6rem;
  }
  .sub-heading-lx {
    font-size: 1.5rem;
  }
}

/* .smort-header .menu-item a { */

/* Header */

.wc-timeline-button-show-cart.right {
  display: none !important;
}

.kb-submit-field .kt-btn-inner-text {
  font-family: "CustomHeadingFont";
  font-size: 20px;
}

.smort-header {
  padding: 10px 0px;
}
.flag-icon {
  height: 20px !important;
}

/* Buttons */

.outline-btn-white-lx a {
  background-color: transparent;
  border-radius: 0px;
  text-align: left;
  padding: 14px;
  min-width: 200px;
  border: 1px solid var(--accentColor);
  min-width: 240px;
  position: relative;
  font-family: var(--fontFamily);
  color: #fff;
  text-transform: uppercase;
}
.outline-btn-white-lx a:after {
  content: var(--arrowRight);
  position: absolute;
  right: 9px;
  top: 13px;
  transition: transform 0.2s ease-in-out;
  filter: invert(1);
}

.outline-btn-black-lx a {
  background-color: transparent;
  border-radius: 0px;
  text-align: left;
  padding: 14px;
  min-width: 200px;
  border: 1px solid #000000;
  min-width: 240px;
  position: relative;
  font-family: var(--fontFamily);
  color: #000;
  text-transform: uppercase;
}
.outline-btn-black-lx a:after {
  content: var(--arrowRight);
  position: absolute;
  right: 9px;
  top: 13px;
}

.main-btn-lx a {
  background-color: var(--accentColor);
  border-radius: 0px;
  text-align: left;
  padding: 10px;
  min-width: 200px;
  border: 1px solid var(--accentColor);
  min-width: 240px;
  position: relative;
  font-family: var(--fontFamily);
}
.main-btn-lx a:after {
  content: var(--arrowRight);
  position: absolute;
  right: 9px;
  filter: brightness(0) invert(1);
  top: 13px;
  transition: transform 0.2s ease-in-out;
}

.cta-row-btn a {
  text-transform: uppercase;
  font-family: "CustomHeadingFont";
  font-size: 17px !important;
  padding: 5px 30px;
}

a.wp-block-button__link:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease-in-out;
}

/* General */

.hero-text {
  font-size: 30px;
  max-width: 400px;
  margin: 0px;
}
button#wc_search_trigger {
  padding: 12px;
}

button#wc_search_trigger {
  border-radius: 30px;
  border: 0px;
}

.usp-header {
  display: flex;
  gap: 10px;
  list-style: none; /* Tar bort punkter */
  padding: 0;
  margin: 0;
}

.usp-header li {
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 11px;
}

.usp-header img {
  display: block;
  max-height: 15px;
  margin-right: 10px;
}
a.af-login {
  color: #f9f9f9;
  padding: 4px 33px;
  font-size: 13px;
  background-color: var(--accentColor3);
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
  border: 1px solid;
}

/* Footer */

.footer-heading {
  font-size: 5rem;
  margin-bottom: 2%;
}

@media (max-width: 992px) {
  .footer-heading {
    font-size: 3rem;
  }
}

.footer-link-row a {
  position: relative;
  text-decoration: none;
  color: inherit; /* Behåller textfärgen */
  padding-bottom: 5px; /* Avstånd för understrykningen */
  font-size: 20px;
  margin: 0px;
}
.footer-link-row h3 {
  margin: 0px 0px 12px 0px;
}
.footer-link-row a::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 2px; /* Tjockleken på linjen */
  background-color: currentColor; /* Använder textens färg */
  transition: width 0.3s ease-in-out;
}

.footer-link-row a:hover::after {
  width: 100%; /* Expanderar linjen vid hover */
}

/*Breadcrumbs */
.woocommerce:where(body:not(.woocommerce-uses-block-theme))
  .woocommerce-breadcrumb {
  font-size: 14px;
  margin: 0px;
  padding: 5px 5px;
  color: #fff;
  text-align: center;
  background-color: #1e2533;
  padding: 10px 0px;
}

@media (max-width: 768px) {
  .woocommerce:where(body:not(.woocommerce-uses-block-theme))
    .woocommerce-breadcrumb {
    font-size: 12px;
    padding: 2px;
  }
}

.woocommerce:where(body:not(.woocommerce-uses-block-theme))
  .woocommerce-breadcrumb
  a {
  color: #fff;
}

.hero-section-lx::after {
  content: "";
  position: absolute;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  background-image: url(/wp-content/uploads/2025/05/arrow-jumping.png);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  animation: jumpAnimation 1.5s infinite ease-in-out;
}

@keyframes jumpAnimation {
  0% {
    transform: translateX(-50%) translateY(0);
  }
  50% {
    transform: translateX(-50%) translateY(-15px);
  }
  100% {
    transform: translateX(-50%) translateY(0);
  }
}
