body,
html {
  margin: 0;
  padding: 0;
}

.smort-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  padding: 0;
}

.smort-logo img {
  display: block;
  margin: 0 auto;
  max-width: none;
}

.smort-nav {
  text-align: center;
}

.smort-nav.smort-nav-centered {
  width: 60%;
  justify-content: center;
}

.smort-nav.smort-nav-left {
  width: 90%;
  justify-content: flex-start;
}

.smort-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  justify-content: center;
  margin-right: 20px;
}

.smort-nav ul li {
  margin: 0 15px;
}

.smort-nav ul li a {
  text-decoration: none;
  color: #000;
}

.smort-nav ul li a:hover,
.smort-nav ul li a:active {
  color: var(--accentColor) !important;
}

.smort-cart {
  width: 25%;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.smort-header-style_2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.smort-header-style_2 .smort-nav {
  width: 100%;
  text-align: left;
}

.smort-header-style_2 .smort-logo {
  width: 20%;
  display: flex;
  justify-content: flex-start !important;
  align-items: center;
  position: relative;
}

.smort-header-style_2 .smort-cart {
  width: 600px;
  text-align: right;
  background-color: rgba(
    255,
    255,
    255,
    0.2
  ); /* Semi-transparent vit bakgrund */
  padding: 10px 15px;
  border-radius: 40px;
  backdrop-filter: blur(10px); /* Här justerar du styrkan på bluret */
  -webkit-backdrop-filter: blur(10px); /* För Safari */
}

@media (max-width: 992px) {
  .smort-header-style_2 .smort-cart {
    background-color: transparent;
  }
}

.smort-header.transparent {
  background: none;
  position: absolute;
  width: 100%;
}

body.transparent-header {
  margin-top: 0;
}
/* Add this to style.css */

/* Burger Menu */
.burger-menu {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 10px;
  width: 30%;
  min-width: 40px;
}

.burger-menu span {
  background: #000;
  height: 2px;
  width: 100%;
  margin: 2px 0;
}
.sub-menu-xk {
  display: flex;
  flex-direction: row;
  width: 100%;
  width: 90%;
  overflow-y: auto;
  text-align: left;
  padding-bottom: 10%;
  border-top: 1px solid #f1f1f1;
  padding-top: 5%;
  gap: 40px;
  text-transform: uppercase;
  font-family: "CustomHeadingFont";
}
.sub-menu-xk a {
  color: #fff;
  font-size: 2rem;
}

.mobile-nav-inner {
  text-align: center;
  width: 100%;
}

.close-nav {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  font-size: 30px;
  color: #fff;
  cursor: pointer;
}

.smort-header .menu-item a {
  text-transform: uppercase;
}

.burger-menu-div {
  display: flex;
  gap: 10px;
  background-color: var(--accentColor);
  padding: 10px 30px;
  border-radius: 30px;
}

.burger-menu-div p {
  margin: 0px;
}

@media (min-width: 992px) {
}

@media (max-width: 992px) {
  .burger-menu {
    display: flex;
  }

  .main-navigation {
    display: none;
  }
}

.become-reseller-link {
  color: #fff;
}

.become-reseller-link:hover {
  color: var(--accentColor);
}
