<?php
// functions.php

function enqueue_custom_styles_scripts()
{
    wp_enqueue_style('child-theme-css', get_stylesheet_directory_uri() . '/style.css', array(), '1.0', 'all');
    wp_enqueue_style('header-theme-css', get_stylesheet_directory_uri() . '/header.css', array(), '1.0', 'all');
    wp_enqueue_style('single-product-css', get_stylesheet_directory_uri() . '/single-product.css', array(), '1.0', 'all');
    wp_enqueue_style('archive-product-css', get_stylesheet_directory_uri() . '/archive-product.css', array(), '1.0', 'all');
    wp_enqueue_script('custom-accordion', get_stylesheet_directory_uri() . '/js/custom-accordion.js', array('jquery'), '1.0', true);
    wp_enqueue_script('custom-variation-script', get_template_directory_uri() . '/js/custom-variation-script.js', array('jquery'), false, true);
}
add_action('wp_enqueue_scripts', 'enqueue_custom_styles_scripts');

/* Enque admin styling */

function mitt_enqueue_admin_style()
{
    wp_enqueue_style('mitt-admin-style', get_stylesheet_directory_uri() . '/style.css');
}
add_action('admin_enqueue_scripts', 'mitt_enqueue_admin_style');


function register_acf_block_types()
{
    acf_register_block_type(array(
        'name' => 'smort-vimeo-widget',
        'title' => __("Smort - Smort Vimeo Widget"),
        'description' => __('Smort block'),
        'render_template' => '/template-parts/smortblocks/smort-vimeo-widget/smort-vimeo-widget.php',
        'category' => 'smort',
        'icon' => 'id',
        'keywords' => array('CTA', 'Block'),
        'mode' => 'edit',
        'enqueue_style' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-vimeo-widget/smort-vimeo-widget.css',
        'enqueue_script' => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-vimeo-widget/smort-vimeo-widget.js'
    ));

    // Logos scroll Smort Hero block
    acf_register_block_type(array(
        'name'                    => 'kundloggorscroll',
        'title'                     => __('Kundloggor - Scroll'),
        'description'          => __('Smort block'),
        'render_template'    => '/template-parts/smortblocks/logos-scroll/logos-scroll.php',
        'category'              => 'smort',
        'icon'                    => 'id',
        'keywords'              => array('CTA', 'Block'),
        'mode'            => 'edit',
        'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/logos-scroll/logos-scroll.css',
        'enqueue_script'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/logos-scroll/logos-scroll.js',
    ));
    // Product slider - Smort   
    acf_register_block_type(array(
        'name'                    => 'Produkt slider - Smort  ',
        'title'                     => __('Produkt slider - Smort'),
        'description'          => __('Smort block'),
        'render_template'    => '/template-parts/smortblocks/product-slider-smort/product-slider-smort.php',
        'category'              => 'smort',
        'icon'                    => 'id',
        'keywords'              => array('CTA', 'Block'),
        'mode'            => 'edit',
        'enqueue_script'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/product-slider-smort/product-slider-smort.js',
        'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/product-slider-smort/product-slider-smort.css',
    ));
    acf_register_block_type(array(
        'name'                    => 'Smort - Kategorier',
        'title'                     => __('Smort - Kategorier karusell'),
        'description'          => __('Smort block'),
        'render_template'    => '/template-parts/smortblocks/smort-cat-carousel/smort-cat-carousel.php',
        'category'              => 'smort',
        'icon'                    => 'id',
        'keywords'              => array('CTA', 'Block'),
        'mode'            => 'edit',
        'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-cat-carousel/smort-cat-carousel.css',
        'enqueue_script'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-cat-carousel/smort-cat-carousel.js',
    ));
    acf_register_block_type(array(
        'name'                    => 'Video / text - Smort Block',
        'title'                     => __('Video / text - Smort Block'),
        'description'          => __('Smort block'),
        'render_template'    => '/template-parts/smortblocks/video-text/video-text.php',
        'category'              => 'smort',
        'icon'                    => 'id',
        'keywords'              => array('CTA', 'Block'),
        'mode'            => 'edit',
        'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/video-text/video-text.css',
        'enqueue_script'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/video-text/video-text.js',
    ));
    acf_register_block_type(array(
        'name'                    => 'Scrollande text - block',
        'title'                     => __('Scrollande text'),
        'description'          => __('Scrollande text.'),
        'render_template'    => '/template-parts/smortblocks/scrollande-text/scrollande-text.php',
        'category'              => 'smort',
        'icon'                    => 'id',
        'keywords'              => array('Scrollande block', 'Smort'),
        'mode'            => 'edit',
        'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/scrollande-text/scrollande-text.css',
        'enqueue_script'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/scrollande-text/scrollande-text.js',
    ));
    acf_register_block_type(array(
        'name'                    => 'CTA Section - block',
        'title'                     => __('CTA Section med bildbyte'),
        'description'          => __('Scrollande text.'),
        'render_template'    => '/template-parts/smortblocks/cta-section/cta-section.php',
        'category'              => 'smort',
        'icon'                    => 'id',
        'keywords'              => array('CTA block', 'Smort'),
        'mode'            => 'edit',
        'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/cta-section/cta-section.css',
        'enqueue_script'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/cta-section/cta-section.js',
    ));
    acf_register_block_type(array(
        'name'                    => 'CTA Video - block',
        'title'                     => __('CTA Section med video hover'),
        'description'          => __('Scrollande text.'),
        'render_template'    => '/template-parts/smortblocks/video-hover/video-hover.php',
        'category'              => 'smort',
        'icon'                    => 'id',
        'keywords'              => array('Video hover', 'Smort'),
        'mode'            => 'edit',
        'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/video-hover/video-hover.css',
        'enqueue_script'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/video-hover/video-hover.js',
    ));
    acf_register_block_type(array(
        'name'                    => 'Smort - Återförsäljarkarta',
        'title'                     => __('Smort - Återforsaljarkarta'),
        'description'          => __('Smort block'),
        'render_template'    => '/template-parts/smortblocks/aterforsaljarkarta/aterforsaljarkarta.php',
        'category'              => 'smort',
        'icon'                    => 'id',
        'keywords'              => array('CTA', 'Block'),
        'mode'            => 'edit',
        'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/aterforsaljarkarta/aterforsaljarkarta.css',
        'enqueue_script'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/aterforsaljarkarta/aterforsaljarkarta.js',
    ));
}
// Check if function exists and hook into setup.
if (function_exists('acf_register_block_type')) {
    add_action('acf/init', 'register_acf_block_types');
}

/* Include ACF field groups */
require_once get_stylesheet_directory() . '/template-parts/smortblocks/smort-vimeo-widget/acf/smort-vimeo-widget.php';



function enqueue_swiper_slider()
{
    // Enqueue Swiper CSS
    wp_enqueue_style('swiper-css', 'https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css', array(), '10.0.0');

    // Enqueue Swiper JS
    wp_enqueue_script('swiper-js', 'https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js', array(), '10.0.0', true);
}
add_action('wp_enqueue_scripts', 'enqueue_swiper_slider');



// Allow SVG file uploads in WordPress
function allow_svg_uploads($mimes)
{
    // Add SVG mime type
    $mimes['svg'] = 'image/svg+xml';
    $mimes['svgz'] = 'image/svg+xml';
    return $mimes;
}
add_filter('upload_mimes', 'allow_svg_uploads');

// Sanitize SVG files for security
function sanitize_svg_upload($file)
{
    $file_type = wp_check_filetype($file['name']);
    if ($file_type['ext'] === 'svg' || $file_type['ext'] === 'svgz') {
        $file['type'] = 'image/svg+xml';
    }
    return $file;
}
add_filter('wp_check_filetype_and_ext', 'sanitize_svg_upload', 10, 4);

// Display SVG previews in the media library
function display_svg_in_media_library($response, $attachment, $meta)
{
    if ($response['type'] === 'image' && $response['subtype'] === 'svg+xml' && class_exists('SimpleXMLElement')) {
        $svg_path = get_attached_file($attachment->ID);
        if (file_exists($svg_path)) {
            $svg_content = file_get_contents($svg_path);
            $xml = simplexml_load_string($svg_content);
            if ($xml !== false) {
                $svg_content = $xml->asXML();
                $response['image'] = [
                    'src' => 'data:image/svg+xml;base64,' . base64_encode($svg_content),
                    'width' => (int) $xml['width'],
                    'height' => (int) $xml['height'],
                ];
            }
        }
    }
    return $response;
}
add_filter('wp_prepare_attachment_for_js', 'display_svg_in_media_library', 10, 3);


// Remove the default WooCommerce ordering dropdown
remove_action('woocommerce_before_shop_loop', 'woocommerce_catalog_ordering', 30);

// Remove the result count text
remove_action('woocommerce_before_shop_loop', 'woocommerce_result_count', 20);


// Remove the default "Add to cart" button in the loop
remove_action('woocommerce_after_shop_loop_item', 'woocommerce_template_loop_add_to_cart', 10);

// Remove add to cart buttom brom product page
remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30);


remove_action('woocommerce_after_shop_loop_item_title', 'woocommerce_template_loop_price', 10);

// Ta bort varukorg och kassa från WooCommerce-menyn
function remove_cart_checkout_pages_from_menu($items, $menu, $args)
{
    foreach ($items as $key => $item) {
        if (in_array(strtolower($item->title), array('varukorg', 'kassa'))) {
            unset($items[$key]);
        }
    }
    return $items;
}
add_filter('wp_get_nav_menu_items', 'remove_cart_checkout_pages_from_menu', 10, 3);


function disable_cart_and_checkout_pages()
{
    if (is_cart() || is_checkout()) {
        wp_redirect(home_url());
        exit;
    }
}
add_action('template_redirect', 'disable_cart_and_checkout_pages');


remove_action('woocommerce_after_shop_loop_item', 'woocommerce_template_loop_add_to_cart', 10);
remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30);

remove_action('woocommerce_after_shop_loop_item_title', 'woocommerce_template_loop_price', 30);

remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_price', 10);

add_filter('woocommerce_add_to_cart_message_html', '__return_null');
add_filter('wc_add_to_cart_message_html', '__return_null');


function enqueue_font_awesome()
{
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css');
}
add_action('wp_enqueue_scripts', 'enqueue_font_awesome');



/* USP under varukorg */

// Function to add content below the add to cart form
function custom_content_below_add_to_cart_form() {}
add_action('woocommerce_after_add_to_cart_form', 'custom_content_below_add_to_cart_form');

// Popup JavaScript
add_action('wp_footer', function () {
?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const openPopupBtn = document.getElementById('open-popup-btn');
            const closePopupBtn = document.getElementById('close-popup-btn');
            const popupOverlay = document.getElementById('popup-overlay');

            // Öppna popup
            openPopupBtn.addEventListener('click', function() {
                popupOverlay.style.display = 'flex';
            });

            // Stäng popup
            closePopupBtn.addEventListener('click', function() {
                popupOverlay.style.display = 'none';
            });

            // Stäng popup om man klickar utanför innehållet
            popupOverlay.addEventListener('click', function(e) {
                if (e.target === popupOverlay) {
                    popupOverlay.style.display = 'none';
                }
            });
        });
    </script>
<?php
});


// Tar bort kvantitetsfältet från enkla produkter
add_filter('woocommerce_is_sold_individually', 'remove_quantity_input', 10, 2);
function remove_quantity_input($return, $product)
{
    return true;
}
add_action('woocommerce_after_add_to_cart_form', 'custom_content_below_add_to_cart_form');






add_theme_support('wc-product-gallery-zoom');
add_theme_support('wc-product-gallery-lightbox');
add_theme_support('wc-product-gallery-slider');

function enqueue_slick_slider()
{
    // Enqueue jQuery (already included by WordPress by default, but ensuring it's loaded)
    wp_enqueue_script('jquery');

    // Enqueue Slick Slider CSS
    wp_enqueue_style('slick-css', 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css', array(), '1.8.1');

    // Enqueue Slick Slider Theme CSS (optional)
    wp_enqueue_style('slick-theme-css', 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css', array(), '1.8.1');

    // Enqueue Slick Slider JS
    wp_enqueue_script('slick-js', 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js', array('jquery'), '1.8.1', true);

    // Enqueue your custom Slick initialization script
}
add_action('wp_enqueue_scripts', 'enqueue_slick_slider');



/* Shortcode anställda */


function visa_anstallda_grid_shortcode()
{
    ob_start();

    // Definiera alla fält där anställda kan vara lagrade
    $field_names = array('anstallda_sverige');

    $anstallda_hittades = false;

?>
    <div class="anstallda-grid-container">
        <?php
        // Loopa genom alla fält och hämta anställda
        foreach ($field_names as $field_name) {
            if (have_rows($field_name, 'option')) {
                while (have_rows($field_name, 'option')) : the_row();
                    $anstallda_hittades = true;
                    $bild = get_sub_field('anstalld_bild');
                    $namn = get_sub_field('anstalld_namn');
                    $roll = get_sub_field('anstalld_roll');
                    $telefonnummer = get_sub_field('anstalld_telefonnummer');
                    $epost = get_sub_field('anstalld_epost');
        ?>
                    <div class="anstalld-item">
                        <?php if ($bild) : ?>
                            <img src="<?php echo esc_url($bild['url']); ?>" alt="<?php echo esc_attr($namn); ?>" class="anstalld-bild">
                        <?php endif; ?>

                        <?php if ($namn) : ?>
                            <h3 class="anstalld-namn"><?php echo esc_html($namn); ?></h3>
                        <?php endif; ?>

                        <?php if ($roll) : ?>
                            <p class="anstalld-roll"><?php echo esc_html($roll); ?></p>
                        <?php endif; ?>

                        <div class="anstalld-kontakt">
                            <?php if ($telefonnummer) : ?>
                                <p><a href="tel:<?php echo esc_attr($telefonnummer); ?>"><?php echo esc_html($telefonnummer); ?></a></p>
                            <?php endif; ?>
                            <?php if ($epost) : ?>
                                <p><a href="mailto:<?php echo esc_attr($epost); ?>"><?php echo esc_html($epost); ?></a></p>
                            <?php endif; ?>
                        </div>
                    </div>
        <?php
                endwhile;
            }
        }
        ?>
    </div>
    <?php

    if (!$anstallda_hittades) {
        echo '<p>Inga anställda hittades.</p>';
    }

    return ob_get_clean();
}
add_shortcode('smort_anstallda', 'visa_anstallda_grid_shortcode');


/* Custom post type - Ambassadör */

// Registrera Custom Post Type "Ambassadörer"
function create_ambassador_cpt()
{
    $labels = array(
        'name'                  => 'Ambassadörer',
        'singular_name'         => 'Ambassadör',
        'menu_name'             => 'Ambassadörer',
        'name_admin_bar'        => 'Ambassadör',
        'add_new'               => 'Lägg till ny',
        'add_new_item'          => 'Lägg till ny Ambassadör',
        'new_item'              => 'Ny Ambassadör',
        'edit_item'             => 'Redigera Ambassadör',
        'view_item'             => 'Visa Ambassadör',
        'all_items'             => 'Alla Ambassadörer',
        'search_items'          => 'Sök Ambassadörer',
        'not_found'             => 'Inga Ambassadörer hittades',
    );

    $args = array(
        'labels'                => $labels,
        'public'                => true,
        'publicly_queryable'    => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'query_var'             => true,
        'rewrite'               => array('slug' => 'ambassador'),
        'capability_type'       => 'post',
        'has_archive'           => true,
        'hierarchical'          => false,
        'menu_position'         => 5,
        'menu_icon'             => 'dashicons-id',
        'supports'              => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'show_in_rest'          => true, // Gör den synlig i blockredigeraren
    );

    register_post_type('ambassador', $args);
}

add_action('init', 'create_ambassador_cpt');

// Shortcode för att visa ambassadörer i en grid
function ambassadorer_grid_shortcode($atts)
{
    ob_start();

    $query = new WP_Query(array(
        'post_type'      => 'ambassador',
        'posts_per_page' => -1, // Ladda alla ambassadörer
        'orderby'        => 'date',
        'order'          => 'DESC'
    ));

    if ($query->have_posts()) {
        echo '<div class="ambassadors-grid">';
        while ($query->have_posts()) {
            $query->the_post();
            $background_image = get_the_post_thumbnail_url(get_the_ID(), 'large');
            $title = get_the_title();
            $permalink = get_permalink();

    ?>
            <a href="<?php echo esc_url($permalink); ?>" class="ambassador-link">
                <div class="ambassador-item" style="background-image: url('<?php echo esc_url($background_image); ?>');">
                    <div class="overlay"></div>
                    <div class="content">
                        <h2><?php echo esc_html($title); ?></h2>
                        <span class="read-more-btn">Läs mer</span>
                    </div>
                </div>
            </a>
<?php
        }
        echo '</div>';
        wp_reset_postdata();
    } else {
        echo '<p>Inga ambassadörer hittades.</p>';
    }

    return ob_get_clean();
}

add_shortcode('ambassador_grid', 'ambassadorer_grid_shortcode');


add_filter('woocommerce_my_account_my_orders_actions', 'bbloomer_order_again_action', 9999, 2);

function bbloomer_order_again_action($actions, $order)
{
    if ($order->has_status('completed')) {
        $actions['order-again'] = array(
            'url' => wp_nonce_url(add_query_arg('order_again', $order->get_id(), wc_get_cart_url()), 'woocommerce-order_again'),
            'name' => __('Order again', 'woocommerce'),
        );
    }
    return $actions;
}




/* Filter area widget */

function custom_theme_widgets_init()
{
    // Register a sidebar (widget area)
    register_sidebar(array(
        'name'          => 'Filter',
        'id'            => 'filter',
        'description'   => 'Product Sidebar',
        'before_widget' => '<div class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ));
}
add_action('widgets_init', 'custom_theme_widgets_init');


function adjust_smort_header_class()
{
    if ((is_product() || is_product_category() || is_archive())  && !is_front_page()) {
        echo '<style>
            .smort-header {
                position: static !important;
            }
        </style>';
    }
}
add_action('wp_head', 'adjust_smort_header_class');
