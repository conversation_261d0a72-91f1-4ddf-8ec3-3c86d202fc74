<?php if ($footer_page_url = get_field('footer_page', 'option')): ?>
    <footer class="smort-footer">
        <?php
        $footer_page_id = url_to_postid($footer_page_url);
        $footer_post = get_post($footer_page_id);
        if ($footer_post) {
            echo apply_filters('the_content', $footer_post->post_content);
        }
        ?>

        <?php
        // Vimeo Popup Widget
        $vimeo_video_id = get_field('vimeo_video_id', 'option');

        if ($vimeo_video_id): ?>
            <div id="vimeo-popup" class="vimeo-popup-overlay" style="display: none;">
                <div class="vimeo-popup-content">
                    <button class="vimeo-popup-close">&times;</button>
                    <div class="vimeo-popup-player">
                        <iframe
                            src="https://player.vimeo.com/video/<?php echo esc_attr($vimeo_video_id); ?>?controls=1&autoplay=0&loop=0&title=0&byline=0&portrait=0"
                            width="100%"
                            height="100%"
                            frameborder="0"
                            allowfullscreen
                            mozallowfullscreen
                            webkitallowfullscreen>
                        </iframe>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <script>
            document.addEventListener("DOMContentLoaded", function() {
                // Existing popup functionality
                const addToCartButton = document.querySelector('.single_add_to_cart_button');
                const popup = document.getElementById('my-custom-popup');
                const closeBtn = document.querySelector('.close-popup');

                if (addToCartButton) {
                    addToCartButton.addEventListener('click', function(e) {
                        e.preventDefault(); // Hindrar WooCommerce från att lägga till i varukorgen
                        popup.style.display = 'block';
                    });
                }

                if (closeBtn) {
                    closeBtn.addEventListener('click', function() {
                        popup.style.display = 'none';
                    });
                }

                // Valfritt: klick utanför popup stänger den
                window.addEventListener('click', function(e) {
                    if (e.target === popup) {
                        popup.style.display = 'none';
                    }
                });

                // Vimeo popup functionality
                const vimeoPopup = document.getElementById('vimeo-popup');
                const vimeoCloseBtn = document.querySelector('.vimeo-popup-close');
                const vimeoTriggerButtons = document.querySelectorAll('.vimeo-popup-trigger');

                // Open Vimeo popup when trigger buttons are clicked
                vimeoTriggerButtons.forEach(function(button) {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        if (vimeoPopup) {
                            vimeoPopup.style.display = 'flex';
                            document.body.style.overflow = 'hidden'; // Prevent background scrolling
                        }
                    });
                });

                // Close Vimeo popup when close button is clicked
                if (vimeoCloseBtn) {
                    vimeoCloseBtn.addEventListener('click', function() {
                        if (vimeoPopup) {
                            vimeoPopup.style.display = 'none';
                            document.body.style.overflow = ''; // Restore scrolling

                            // Stop the video by reloading the iframe
                            const iframe = vimeoPopup.querySelector('iframe');
                            if (iframe) {
                                const src = iframe.src;
                                iframe.src = '';
                                iframe.src = src;
                            }
                        }
                    });
                }

                // Close Vimeo popup when clicking outside the content
                if (vimeoPopup) {
                    vimeoPopup.addEventListener('click', function(e) {
                        if (e.target === vimeoPopup) {
                            vimeoPopup.style.display = 'none';
                            document.body.style.overflow = ''; // Restore scrolling

                            // Stop the video by reloading the iframe
                            const iframe = vimeoPopup.querySelector('iframe');
                            if (iframe) {
                                const src = iframe.src;
                                iframe.src = '';
                                iframe.src = src;
                            }
                        }
                    });
                }

                // Close popup with Escape key
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && vimeoPopup && vimeoPopup.style.display === 'flex') {
                        vimeoPopup.style.display = 'none';
                        document.body.style.overflow = ''; // Restore scrolling

                        // Stop the video by reloading the iframe
                        const iframe = vimeoPopup.querySelector('iframe');
                        if (iframe) {
                            const src = iframe.src;
                            iframe.src = '';
                            iframe.src = src;
                        }
                    }
                });
            });
        </script>




    </footer>
<?php endif; ?>
<?php wp_footer(); ?>
</body>

</html>