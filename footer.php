<?php if ($footer_page_url = get_field('footer_page', 'option')): ?>
    <footer class="smort-footer">
        <?php
        $footer_page_id = url_to_postid($footer_page_url);
        $footer_post = get_post($footer_page_id);
        if ($footer_post) {
            echo apply_filters('the_content', $footer_post->post_content);
        }
        ?>

        <?php
        // Lägg till detta i footer.php
        ?>


        <script>
            document.addEventListener("DOMContentLoaded", function() {
                const addToCartButton = document.querySelector('.single_add_to_cart_button');
                const popup = document.getElementById('my-custom-popup');
                const closeBtn = document.querySelector('.close-popup');

                if (addToCartButton) {
                    addToCartButton.addEventListener('click', function(e) {
                        e.preventDefault(); // Hindrar WooCommerce från att lägga till i varukorgen
                        popup.style.display = 'block';
                    });
                }

                if (closeBtn) {
                    closeBtn.addEventListener('click', function() {
                        popup.style.display = 'none';
                    });
                }

                // Valfritt: klick utanför popup stänger den
                window.addEventListener('click', function(e) {
                    if (e.target === popup) {
                        popup.style.display = 'none';
                    }
                });
            });
        </script>




    </footer>
<?php endif; ?>
<?php wp_footer(); ?>
</body>

</html>