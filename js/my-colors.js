document.addEventListener('DOMContentLoaded', function () {
    /**
     * <PERSON><PERSON> färglistan (My Colors-sektion)
     */
    const colorsList = document.getElementById('colors-list');
    const addColorButton = document.getElementById('add-color');

    if (colorsList && addColorButton) {
        // Lägg till en ny rad
        addColorButton.addEventListener('click', () => {
            const rowIndex = colorsList.children.length;
            const row = document.createElement('div');
            row.classList.add('color-row');
            row.innerHTML = `
                <input type="text" name="colors[${rowIndex}][title]" placeholder="Title">
                <input type="color" name="colors[${rowIndex}][hex]" value="#000000">
                <button type="button" class="remove-color">X</button>
            `;
            colorsList.appendChild(row);
        });

        // Ta bort en rad
        colorsList.addEventListener('click', (event) => {
            if (event.target.classList.contains('remove-color')) {
                const colorRow = event.target.parentElement;
                colorRow.remove(); // Tar bort raden från DOM
            }
        });
    }

    /**
     * Hantera lackval och kulörval på produktsidan
     */
    const varnishButtons = document.querySelectorAll('.varnish-button');
    const varnishInput = document.getElementById('varnish_type');
    const colorButtons = document.querySelectorAll('.color-button');
    const colorInput = document.getElementById('paint_color');

    // Hantera lackval
    if (varnishButtons.length > 0 && varnishInput) {
        varnishButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Avmarkera alla andra knappar
                varnishButtons.forEach(btn => btn.classList.remove('active'));
                // Markera vald knapp
                button.classList.add('active');
                // Spara valt värde
                varnishInput.value = button.getAttribute('data-varnish');
                console.log(`Selected Varnish: ${varnishInput.value}`);
            });
        });
    }

    // Hantera kulörval
    if (colorButtons.length > 0 && colorInput) {
        colorButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Avmarkera alla andra knappar
                colorButtons.forEach(btn => btn.classList.remove('active'));
                // Markera vald knapp
                button.classList.add('active');
                // Spara valt värde
                colorInput.value = button.getAttribute('data-color');
                console.log(`Selected Color: ${colorInput.value}`);
            });
        });
    }
});


document.addEventListener('DOMContentLoaded', function () {
    // Hantera lackval
    const varnishButtons = document.querySelectorAll('.varnish-button');
    const varnishInput = document.getElementById('varnish_type');
    const estimatedPrice = document.createElement('div');
    estimatedPrice.classList.add('estimated-price');
    estimatedPrice.style.marginTop = '10px';
    estimatedPrice.style.fontSize = '16px';
    estimatedPrice.style.color = '#333';

    // Lägg till den uppskattade pris-raden efter lackvalen
    const varnishContainer = document.querySelector('.varnish-buttons');
    if (varnishContainer) {
        varnishContainer.insertAdjacentElement('afterend', estimatedPrice);
    }

    varnishButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Avmarkera alla andra knappar
            varnishButtons.forEach(btn => btn.classList.remove('active'));
            // Markera vald knapp
            button.classList.add('active');
            // Spara valt värde
            varnishInput.value = button.getAttribute('data-varnish');

            // Visa det uppskattade priset
            if (varnishInput.value === '3') {
                estimatedPrice.textContent = 'Estimated price: 2999 SEK';
            } else {
                estimatedPrice.textContent = ''; // Döljer pris för andra val
            }
        });
    });
});

document.addEventListener('DOMContentLoaded', function () {
    const colorButtons = document.querySelectorAll('.color-button');
    const colorInput = document.getElementById('paint_color');
    const colorHelpText = document.getElementById('color-help-text');
    const helpTextField = document.getElementById('help-text-field');

    colorButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active state from all buttons
            colorButtons.forEach(btn => btn.classList.remove('active'));
            // Add active state to clicked button
            button.classList.add('active');
            
            // Save color value and title
            const colorValue = button.getAttribute('data-color');
            const colorTitle = button.textContent.trim();

            colorInput.value = JSON.stringify({ color: colorValue, title: colorTitle });

            // Display help text and field if "I want help with colors" is selected
            if (colorValue === 'help') {
                colorHelpText.textContent = 'Free service, increase delivery by 2 weeks';
                helpTextField.style.display = 'block';
            } else {
                colorHelpText.textContent = '';
                helpTextField.style.display = 'none';
            }
        });
    });
});


