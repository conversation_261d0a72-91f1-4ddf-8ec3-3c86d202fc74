document.addEventListener('DOMContentLoaded', function () {
  const track = document.querySelector('.logos-track');
  const trackWidth = track.scrollWidth;
  let position = 0;
  const speed = 1; // Adjust speed as needed

  function animate() {
      position -= speed;
      if (Math.abs(position) >= trackWidth / 2) { 
          position = 0; 
      }
      track.style.transform = `translateX(${position}px)`;
      requestAnimationFrame(animate);
  }

  animate();
});
