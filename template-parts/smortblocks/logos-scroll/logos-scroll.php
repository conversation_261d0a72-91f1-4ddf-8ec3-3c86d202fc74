<?php if( have_rows('logos') ): ?>
    <div class="logos-slider">
        <?php 
        // Get the scrolling direction from the ACF field
        $scroll_direction = get_field('scroll_direction'); 
        $track_class = $scroll_direction === 'right_to_left' ? 'track-right' : 'track-left';
        ?>
        
        <!-- Single row of logos with dynamic scrolling direction -->
        <div class="logos-track <?php echo esc_attr($track_class); ?>">
            <?php 
            // Output the "logo" sub-field logos multiple times for seamless scrolling
            for ($i = 0; $i < 4; $i++) { // Increase loop count to ensure enough logos for smooth scrolling
                while( have_rows('logos') ): the_row(); 
                    $logo = get_sub_field('logo'); 
					$caselink = get_sub_field('caselink');
                    if( $logo ): ?>
                        <div class="logo-item">
                            <a href="<?php echo $caselink ?>">
								<img src="<?php echo esc_url($logo['url']); ?>" alt="<?php echo esc_attr($logo['alt']); ?>">
							</a>
                        </div>
                    <?php endif; 
                endwhile; 
                reset_rows();
            } ?>
        </div>
    </div>
<?php endif; ?>
