.logos-slider {
  overflow: hidden;
  position: relative;
  width: 100%;
}

.logos-track {
  display: flex;
  gap: 10px; /* Space between logos */
  margin: 10px 0;
  flex-wrap: nowrap; /* Prevent wrapping */
}

.track-left {
  animation: scroll-left 60s linear infinite;
}

.track-right {
  animation: scroll-right 60s linear infinite;
}

.logo-item {
  flex: 0 0 auto;
  padding: 2%;
  border-radius: 20px;
  height: 70px;
  width: 160px;
  align-items: center;
  justify-content: center;
  display: flex;

}

.logo-item img {
  max-width: 150px;
  height: auto;
  display: block;
  object-fit: contain;
  max-height: 100px;
}

/* Keyframes for scrolling left to right */
@keyframes scroll-left {
  0% {
      transform: translateX(-100%);
  }
  100% {
      transform: translateX(0);
  }
}

/* Keyframes for scrolling right to left */
@keyframes scroll-right {
  0% {
      transform: translateX(0);
  }
  100% {
      transform: translateX(-100%);
  }
}
