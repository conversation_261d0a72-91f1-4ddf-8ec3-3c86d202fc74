<?php
// Hämta ACF-fältens värden
$videoklipp_lank = get_field('videoklipp_lank');
$bild_lank = get_field('bild_lank');
$titel = get_field('titel');
$text_under = get_field('text_under');
$knapptext = get_field('knapptext');
$lank = get_field('lank');
?>

<div class="blend-mode-container">
    <?php if ($videoklipp_lank) : ?>
        <video autoplay muted loop playsinline class="background-video">
            <source src="<?php echo esc_url($videoklipp_lank); ?>" type="video/mp4">
            Din webbläsare stöder inte videouppspelning.
        </video>
    <?php elseif ($bild_lank) : ?>
        <img src="<?php echo esc_url($bild_lank); ?>" alt="Bakgrundsbild" class="background-image" />
        <div class="blend-mode-overlay"></div>
    <?php endif; ?>

    <?php if ($titel) : ?>
        <h2 class="blend-mode-text"><?php echo esc_html($titel); ?></h2>
    <?php endif; ?>

    <?php if ($text_under) : ?>
        <p class="blend-mode-paragraph"><?php echo esc_html($text_under); ?></p>
    <?php endif; ?>

    <!---- Hotspots --->
    <?php if (have_rows('hotspots')) : ?>
    <div class="hotspot-wrapper">
        <?php while (have_rows('hotspots')) : the_row();
            $x = get_sub_field('x-led');
            $y = get_sub_field('y-led');
            $titel = get_sub_field('titel');
        ?>
            <div class="hotspot" style="left: <?php echo esc_attr($x); ?>%; top: <?php echo esc_attr($y); ?>%;">
                <div class="hotspot-title"><?php echo esc_html($titel); ?></div>
                <div class="hotspot-plus">+</div>
            </div>
        <?php endwhile; ?>
    </div>
<?php endif; ?>

</div>
