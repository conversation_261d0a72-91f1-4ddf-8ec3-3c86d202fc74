/* För containern med videon */
.blend-mode-container {
  position: relative; /* Behövs för isolation och z-index */
  height: 900px; /* Sektionens höjd */
  display: flex; /* Flexbox för textcentrering */
  flex-direction: column; /* Lägg elementen i en kolumn */
  justify-content: flex-start; /* Börjar högst upp */
  align-items: center; /* Centrerar alla element horisontellt */
  isolation: isolate; /* Viktigt för mix-blend-mode */
  overflow: visible; /* Till<PERSON>ter innehållet att synas utanför containerns gränser */
}

/* Bakgrundsvideon */
.background-video {
  position: absolute; /* Placera videon i bakgrunden */
  top: 0;
  left: 0;
  width: 100%; /* Täcker hela bredden */
  height: 100%; /* Täcker hela höjden */
  object-fit: cover; /* Behåll proportioner och fyll containern */
  z-index: 0; /* Ligger under texten */
}

/* För rubriken */
.blend-mode-text {
  color: #ffffff; /* Vit text som grund */
  font-size: 8rem; /* Storlek på rubriken */
  line-height: 1; /* Radavstånd */
  text-align: center; /* Centrerad text */
  mix-blend-mode: difference; /* Skapar kontrasteffekt */
  z-index: 1; /* Se till att texten ligger ovanpå videon */
  position: relative; /* Behåll textens position i förhållande till bakgrunden */
  margin-top: -100px;
  margin-bottom: 0px;
  max-width: 1000px;
}

/* För paragrafen */
.blend-mode-paragraph {
  color: #ffffff;
  font-size: 1.5rem;
  text-align: center;
  mix-blend-mode: difference;
  z-index: 1;
  position: relative;
  margin: 10px 0 0;
  color: var(--accentColor);
}

/* För länken */
.blend-mode-link {
  color: #ffffff;
  font-size: 17px;
  text-align: center;
  mix-blend-mode: difference;
  z-index: 1;
  position: relative;
  margin-top: 10px;
  text-decoration: none;
  border: 1px solid;
  transition: all 0.3s ease;
  text-transform: uppercase;
  padding: 13px;
  font-family: 'CustomHeadingFont';
  min-width: 200px;
  text-align: left;
  margin-top: 20px;
}

.blend-mode-link:after {
  content: var(--arrowRight);
  position: absolute;
  right: 9px;
  top: 13px;
  transition: transform 0.2s ease-in-out;
}

.blend-mode-link:hover {
  color: #ff9900; /* Ändra färg vid hovring */
  border-bottom: 1px solid #ff9900; /* Ändra linjens färg vid hovring */
}

@media screen and (max-width: 992px) {
.blend-mode-text{
  font-size: 4rem;
}
.blend-mode-container{
   height: 450px;
}
.blend-mode-text{
  margin-top: -50px; 
}
}
.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}


.hotspot-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none; /* Låt inte hotspots blockera klick på andra element */
}

.hotspot {
  position: absolute;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  pointer-events: auto;
}

.hotspot-title {
  background: black;
  color: white;
  padding: 6px 12px;
  font-weight: bold;
  font-size: 14px;
  text-transform: uppercase;
  margin-bottom: 5px;
  white-space: nowrap;
  font-family: 'CustomHeadingFont', sans-serif;
}

.hotspot-plus {
  width: 30px;
  height: 30px;
  background-color: #d9e536;
  border-radius: 50%;
  color: black;
  font-weight: bold;
  font-size: 20px;
  line-height: 30px;
  text-align: center;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
      box-shadow: 0 0 0 0 rgba(217, 229, 54, 0.6);
  }
  70% {
      box-shadow: 0 0 0 10px rgba(217, 229, 54, 0);
  }
  100% {
      box-shadow: 0 0 0 0 rgba(217, 229, 54, 0);
  }
}


.blend-mode-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background: linear-gradient(to bottom, rgb(16 16 23), rgba(0, 0, 0, 0));
  pointer-events: none;
}
