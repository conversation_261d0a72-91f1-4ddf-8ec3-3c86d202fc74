.infinite-text-wrapper {
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  padding: 10px 0;
  position: relative;
  color:var(--accentColor);
}

.infinite-text-track {
  display: inline-block;
  white-space: nowrap;
  animation: scroll-left 60s linear infinite;
}

.scroll-word {
  display: inline-block;
  padding: 0 30px; /* styr avståndet runt varje ord */
  font-size: 18px;
  color: #fff;
}

.scroll-separator {
  display: inline-block;
  color: var(--accentColor);
  font-weight: bold;
  padding: 0 20px; /* styr avståndet kring | */
  font-size: 20px;
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-33.33%);
  }
}
