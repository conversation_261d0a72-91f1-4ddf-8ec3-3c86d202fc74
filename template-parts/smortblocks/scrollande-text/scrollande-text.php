<div class="infinite-text-wrapper">
  <div class="infinite-text-track">
    <?php
    if (have_rows('scrollande_text')) :
        $texts = [];
        while (have_rows('scrollande_text')) : the_row();
            $texts[] = get_sub_field('text');
        endwhile;

        for ($i = 0; $i < 3; $i++) : // Upprepa 3 gånger för sömlö<PERSON> scroll
            foreach ($texts as $word) :
                echo '<span class="scroll-word">' . esc_html($word) . '</span>';
                echo '<span class="scroll-separator">|</span>';
            endforeach;
        endfor;
    endif;
    ?>
  </div>
</div>
