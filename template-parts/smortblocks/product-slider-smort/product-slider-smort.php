<?php
$selected_products = get_field('product_category');

if ($selected_products) {
    $args = array(
        'post_type' => 'product',
        'post__in' => $selected_products, // Use the IDs returned by ACF
        'posts_per_page' => -1,
        'orderby' => 'post__in', // Maintain the same order as selected
    );

    $loop = new WP_Query($args);

    if ($loop->have_posts()) : ?>
        <div class="swiper-container swiper-container-1 product-carousel woocommerce">
            <div class="pagination-wrapper">
                <div class="swiper-button-prev"></div> <!-- ← Vänsterpil först -->
                <div class="swiper-button-next"></div> <!-- → Högerpil sen -->
            </div>
            <div class="swiper-wrapper">
                <?php while ($loop->have_posts()) : $loop->the_post(); ?>
                    <div class="swiper-slide product-slide">
                        <a href="<?php the_permalink(); ?>">
                            <?php woocommerce_show_product_sale_flash(); ?>

                            <?php
                            global $product;

                            $egenskaper = get_field('egenskaper', $product->get_id());

                            if (str_contains($egenskaper, '|') !== false) {
                                $egenskaper = str_replace('|', '<span class="separator">|</span>', $egenskaper);
                            }



                            if (has_post_thumbnail($product->get_id())) {
                                echo wp_get_attachment_image(get_post_thumbnail_id($product->get_id()), 'full');
                            } else {
                                // Fallback image if no thumbnail is set
                                echo '<img class="placeholder" src="' . wc_placeholder_img_src() . '" alt="Placeholder" />';
                            }
                            ?>
                            <div class="slider-content-container">
                                <div class="product-attribute-slider"><?= $egenskaper ?></div>
                                <h2 class="woocommerce-loop-product__title product-slider-title"><?php the_title(); ?></h2>
                                <?php
                                // Hämta produktbeskrivningen
                                $excerpt = $product->get_short_description();
                                if (empty($excerpt)) {
                                    $excerpt = $product->get_description();
                                }

                                // Begränsa till ca 10 ord och lägg till ..
                                if ($excerpt) {
                                    $short_excerpt = wp_trim_words($excerpt, 20, '..');
                                    echo '<p class="product-slider-text">' . esc_html($short_excerpt) . '</p>';
                                } else {
                                    // Fallback om ingen beskrivning finns
                                    echo '<p class="product-slider-text">Produktinformation saknas..</p>';
                                }
                                ?>
                                <div class="cta-div">
                                </div>
                            </div>
                        </a>
                    </div>
                <?php endwhile; ?>
            </div>
        </div>
<?php endif;

    wp_reset_postdata();
}
?>