document.addEventListener("DOMContentLoaded", function () {
  // Wait for images to load before initializing Swiper
  function initializeSwiper() {
    var swiper = new Swiper(".swiper-container", {
      slidesPerView: 3.5, // Display 3 products per slide
      spaceBetween: 10,
      navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
        dynamicBullets: true,
      },
      breakpoints: {
        768: {
          slidesPerView: 3.5, // Adjust for smaller screens
        },
        480: {
          slidesPerView: 1, // Adjust for even smaller screens
        },
        0: {
          slidesPerView: 1, // Adjust for even smaller screens
        },
      },
      // Force update after initialization
      on: {
        init: function () {
          // Force update pagination after a short delay
          setTimeout(() => {
            this.update();
            this.pagination.update();
          }, 100);
        },
        imagesReady: function () {
          // Update when images are loaded
          this.update();
          this.pagination.update();
        }
      }
    });

    // Additional update after all images are loaded
    const images = document.querySelectorAll('.swiper-container img');
    let loadedImages = 0;

    if (images.length === 0) {
      // No images, update immediately
      setTimeout(() => {
        swiper.update();
        swiper.pagination.update();
      }, 100);
    } else {
      images.forEach(img => {
        if (img.complete) {
          loadedImages++;
        } else {
          img.addEventListener('load', () => {
            loadedImages++;
            if (loadedImages === images.length) {
              swiper.update();
              swiper.pagination.update();
            }
          });
        }
      });

      // If all images are already loaded
      if (loadedImages === images.length) {
        setTimeout(() => {
          swiper.update();
          swiper.pagination.update();
        }, 100);
      }
    }
  }

  // Initialize Swiper
  initializeSwiper();
});
