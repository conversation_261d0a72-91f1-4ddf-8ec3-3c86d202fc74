/* === Produktkarusell === */
.product-carousel {
  padding: 0;
  overflow: hidden;
}

.swiper-carousel-wrapper {
  position: relative;
  padding-top: 60px; /* Skapa plats för navigationen ovanför */
  overflow: hidden;
}

/* === Navigationspilar - flyttad till top right === */
.pagination-wrapper {
  position: absolute;
  top: -50px;
  right: 4rem;
  display: flex;
  gap: 10px;
  z-index: 10;
}

@media (max-width: 768px) {
  .pagination-wrapper {
    bottom: -38rem;
    right: 50%;
  }
}

.swiper-button-next,
.swiper-button-prev {
  color: var(--accentColor);
  border: 1px solid var(--accentColor);
  border-radius: 0;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
  background: var(--accentColor);
  color: #fff;
  border-color: var(--accentColor);
}

.swiper-button-next:after,
.swiper-button-prev:after {
  font-size: 15px;
}

/* === Produktkort === */
.product-slide {
  overflow: visible;
  border-radius: 0;
}

.product-slide img {
  width: calc(100% - 20px);
  height: 350px;
  object-fit: contain;
  margin: 10px;
  background-color: #161821;
  border-radius: 0 !important;
}

.product-slide img.placeholder {
  object-position: bottom !important;
}

/* === Swiperstruktur === */
.swiper-slide {
  height: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.swiper-wrapper {
  display: flex;
  align-items: stretch;
}

/* === Produktinfo === */
h2.woocommerce-loop-product__title {
  font-size: 1.7rem;
  padding-left: 15px;
  padding-top: 10px;
  color: #fff;
  max-width: 100%;
  margin: 0;
  line-height: 1.5;
}

.product-category {
  margin-top: 5px;
  font-size: 0.9em;
  color: #555;
}

.product-category a {
  color: var(--accentColor3);
  display: block;
}

.product-varumarke {
  color: #000;
  margin-top: 10px;
}

span.product-parent-category {
  padding-left: 15px;
  font-size: 12px;
  color: #000;
}

/* === Pris === */
.price {
  margin: 0;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
  gap: 5px;
}

.product-slide .amount {
  font-size: 20px;
  display: block;
  margin-top: 15px !important;
}

.price del bdi {
  color: #8e8e8e;
  font-size: 19px;
}

.swiper-slide span.price bdi {
  font-size: 19px;
  text-transform: uppercase;
  font-family: "CustomHeadingFont";
}

.swiper-slide span.price ins {
  text-decoration: none;
}

/* === Sale badge === */
span.onsale {
  position: absolute;
  top: 20px;
  right: 20px;
  color: #fff;
  background-color: var(--accentColor);
  border-radius: 30px;
  padding: 10px;
  font-size: 15px;
}

/* === CTA & footer === */
.cta-div {
  padding-bottom: 20px;
  padding-left: 15px;
  margin-top: 10px;
}

a.product-slider-cta {
  text-align: center !important;
  display: block;
  background-color: var(--accentColor);
  color: #fff;
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
  padding: 10px;
  width: calc(100% - 30px);
}

.product-slider-cta img {
  width: 50px;
  height: 50px;
  object-fit: contain;
  margin: 0;
  border: 0 !important;
}

img.cta-arrow {
  width: 20px;
  height: 20px;
  margin: 5px 0 0;
}

/* === Swiper pagination === */
.swiper-pagination {
  margin-top: 15px;
  text-align: center;
  bottom: -30px !important;
}

.swiper-pagination-bullet {
  background: #333;
  opacity: 0.7;
}

.swiper-pagination-bullet-active {
  background: #4c8077;
  opacity: 1;
}

.swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer;
  width: 20px;
  border-radius: 5px;
}

/* === Stjärnbetyg === */
.woocommerce .star-rating {
  display: inline-block;
  position: relative;
  font-size: 1.2em;
  color: #ffcc00;
  line-height: 1;
  z-index: 10;
}

.woocommerce .star-rating::before {
  content: "★★★★★";
  color: #ccc;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1;
}

.woocommerce .star-rating span {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  overflow: hidden;
  color: #ffcc00;
  z-index: 2;
}

.woocommerce .star-rating span::before {
  content: "★★★★★";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 3;
}

.woocommerce-loop-rating {
  position: relative;
  z-index: 9999;
}

.swiper-button-prev::after {
  content: "\2190"; /* ← */
  font-size: 18px;
}

.swiper-button-next::after {
  content: "\2192";
  font-size: 18px;
}
p.product-slider-text {
  font-size: 14px;
  color: #fff;
  line-height: 1.7;
  margin-top: 5px;
}

.product-attribute-slider {
  font-size: 14px !important;
  line-height: 1.5;
  color: #fff;
  text-transform: uppercase;
  margin-bottom: 3px;
}

.slider-content-container {
  padding-left: 14px;
}

.swiper-button-next,
.swiper-rtl {
  right: -60px;
}

.swiper-button-prev,
.swiper-rtl {
  left: -60px;
}
