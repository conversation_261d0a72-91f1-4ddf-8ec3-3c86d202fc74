document.addEventListener("DOMContentLoaded", function () {
  const videoBoxes = document.querySelectorAll(".video-hover-box");
  const isMobile = window.innerWidth < 768; // Kontrollera om det är mobilversion

  videoBoxes.forEach((box) => {
    const video = box.querySelector(".hover-video");

    if (isMobile) {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            console.log("Is intersecting:", entry.isIntersecting); // Debug

            if (entry.isIntersecting) {
              if (video) {
                video.muted = true;
                video.currentTime = 0;
                video.addEventListener("canplay", () => {
                  video.play();
                });
                box.classList.add("hover-active");
              }
            } else {
              if (video) {
                video.pause();
                box.classList.remove("hover-active");
              }
            }
          });
        },
        {
          threshold: 0.2,
        }
      );

      observer.observe(box);
    } else {
      box.addEventListener("mouseenter", () => {
        if (video) {
          video.currentTime = 0;
          video.play();
        }
      });

      box.addEventListener("mouseleave", () => {
        if (video) {
          video.pause();
        }
      });
    }
  });

  // Lägg till custom cursor-element i DOM
  const cursor = document.createElement("div");
  cursor.classList.add("custom-cursor");
  document.body.appendChild(cursor);

  const hoverGrid = document.querySelector(".video-hover-grid");

  if (hoverGrid) {
    // Visa cursor inom grid
    hoverGrid.addEventListener("mouseenter", () => {
      cursor.style.opacity = "1";
    });

    hoverGrid.addEventListener("mouseleave", () => {
      cursor.style.opacity = "0";
    });

    // Flytta cursor med musen
    document.addEventListener("mousemove", (e) => {
      cursor.style.top = e.clientY + "px";
      cursor.style.left = e.clientX + "px";
    });
  }
});

document.addEventListener("DOMContentLoaded", function () {
  let swiperInstance = null;

  function initSwiper() {
    if (!swiperInstance) {
      swiperInstance = new Swiper(".swiper-container-video", {
        slidesPerView: 1,
        spaceBetween: 10,
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        breakpoints: {
          0: {
            slidesPerView: 1,
            spaceBetween: 10,
          },
          768: {
            slidesPerView: 3,
            spaceBetween: 20,
          },
        },
      });
    }
  }
  initSwiper();
});
