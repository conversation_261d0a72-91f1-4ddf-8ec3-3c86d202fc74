.video-hover-grid {
  display: grid;
  gap: 20px;
  padding: 40px 0;
}

@media screen and (max-width: 768px) {
  .video-hover-grid {
    display: block;
  }
}

/* Dynamiska kolumner */
.video-hover-grid.columns-1 {
  grid-template-columns: 1fr;
}
.video-hover-grid.columns-2 {
  grid-template-columns: 1fr 1fr;
}
.video-hover-grid.columns-3 {
  grid-template-columns: 1fr 1fr 1fr;
}
.video-hover-grid.columns-4 {
  grid-template-columns: 1fr 1fr 1fr 1fr;
}
.video-hover-grid.columns-5 {
  grid-template-columns: repeat(5, 1fr);
}

/* Box */
.video-hover-box {
  position: relative;
  display: block;
  height: 600px;
  overflow: hidden;
  text-decoration: none;
  color: white;
}

@media screen and (max-width: 768px) {
  .video-hover-box {
    margin-bottom: 1rem;
  }
}

.video-wrapper {
  width: 100%;
  height: 100%;
  filter: grayscale(40%);
  transition: filter 0.4s ease;
  pointer-events: none; /* för att klick ska gå till länken */
}

.hover-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* När man hovrar – ta bort grayscale */
.video-hover-box:hover .video-wrapper {
  filter: grayscale(0%);
}

/* Titel */
.video-hover-title {
  font-size: 24px;
  font-weight: bold;
  color: #fff;
  margin: 0 0 5px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.6);
  transition: transform 0.3s ease;
}

/* Forklaring – gömd tills hover */
.video-forklaring {
  font-size: 16px;
  color: #fff;
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.4s ease, transform 0.4s ease;
  margin: 0;
  line-height: 1;
}
/* När du hovrar över boxen */
.video-hover-box:hover .video-hover-title {
  transform: translateY(-10px);
}

.video-hover-box:hover .video-forklaring {
  opacity: 1;
  transform: translateY(0);
}

/* När videon är synlig i mobilversionen */
.video-hover-box.hover-active .video-hover-title {
  transform: translateY(-10px);
}

.video-hover-box.hover-active .video-forklaring {
  opacity: 1;
  transform: translateY(0);
}

.video-hover-box.hover-active .video-wrapper {
  filter: grayscale(0%);
}

/* Nummer i hörnet */
.video-index {
  position: absolute;
  top: 20px;
  left: 20px;
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  z-index: 2;
  mix-blend-mode: difference;
}

/* Innehåll container */
.video-hover-content {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  transition: all 0.4s ease;
}

/* Custom muspekare */
.custom-cursor {
  position: fixed;
  top: 0;
  left: 0;
  width: 70px;
  height: 70px;
  background-color: #f1ec47;
  border-radius: 50%;
  pointer-events: none;
  mix-blend-mode: difference;
  z-index: 9999;
  transform: translate(-50%, -50%);
  transition: transform 0.1s ease-out, opacity 0.2s;
  opacity: 0;
}

/* Övergång på kolumner */
.video-hover-grid {
  display: flex;
  gap: 20px;
  padding: 0px 0;
}

@media screen and (max-width: 768px) {
  .video-hover-grid {
    display: block;
  }
}

.video-hover-box {
  flex: 1 1 0%;
  height: 600px;
  position: relative;
  overflow: hidden;
  text-decoration: none;
  color: white;
  transition: flex 0.2s ease;
  border-radius: 8px;
}
.video-hover-box:hover {
  flex: 1.2;
  z-index: 1;
}

.swiper-slide-video {
  height: 500px;
}

.swiper-container {
  width: 100%;
  overflow: hidden;
}

.swiper-wrapper {
  display: flex;
  transition: transform 0.3s ease;
}

.swiper-slide {
  flex-shrink: 0;
  width: 100%;
  box-sizing: border-box;
}

.video-hover-pagnation span {
  background-color: var(--accentColor);
}

.video-hover-pagnation .swiper-pagination-bullet {
}
