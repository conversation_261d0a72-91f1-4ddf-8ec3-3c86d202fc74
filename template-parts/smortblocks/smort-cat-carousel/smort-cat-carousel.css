.kategori-karusell-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.swiper-container-kategori {
  width: 100%;
}

.swiper-wrapper {
  display: flex;
  transition: transform 0.3s ease;
}

.swiper-slide.kategori-item {
  flex-shrink: 0;
  width: 100%;
  height: 550px;
  background-size: cover;
  background-position: center;
  position: relative;
  border-radius: 0px;
  overflow: hidden;
  text-decoration: none;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
  color: #fff;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5); /* Mörkt overlay */
  border-radius: 0px;
  z-index: 1;
}

.kategori-info {
  position: relative;
  z-index: 2;
  padding: 20px;
  max-width: 90%;
  margin: 0;
  display: flex;
  flex-direction: column;
  height: 50%;
  justify-content: space-between;
  align-items: flex-start;
}

.kategori-title {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 20px;
  margin-top: 10px;
  color: #fff;
}

.kategori-text {
  font-size: 1.2rem;
  line-height: 1.5;
  color: #ddd;
}

@media screen and (max-width: 992px) {
  .swiper-slide.kategori-item{
      height: 450px;
  }
  .kategori-text{
    font-size: 0.8rem;
  }
}

.swiper-slide.kategori-item:hover {
  transform: scale(0.97);
  transition-duration: 0.2s;
}