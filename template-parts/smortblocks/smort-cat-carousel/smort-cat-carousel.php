<?php
/*
Template Name: <PERSON><PERSON><PERSON>
*/

// Enqueue Swiper JS and CSS
wp_enqueue_script('swiper-js', 'https://unpkg.com/swiper/swiper-bundle.min.js', [], null, true);
wp_enqueue_style('swiper-css', 'https://unpkg.com/swiper/swiper-bundle.min.css');

// Kontrollera om repeaterfältet finns
if (have_rows('kategoriobjekt')) : ?>
    <div class="kategori-karusell-container">
        <div class="swiper-container-kategori">
            <div class="swiper-wrapper">
                <?php while (have_rows('kategoriobjekt')) : the_row(); 
                    // Hämta värden från repeaterfältets subfält
                    $background_image = get_sub_field('bakgrundsbild');
                    $title = get_sub_field('rubrik');
                    $short_text = get_sub_field('kort_text');
                    $link = get_sub_field('lank'); 
                ?>
                <!-- <PERSON><PERSON><PERSON> ett karusellobjekt -->
                <a href="<?php echo esc_url($link); ?>" class="swiper-slide kategori-item" style="background-image: url('<?php echo esc_url($background_image); ?>');">
                    <div class="overlay"></div>
                    <div class="kategori-info">
                        <h2 class="kategori-title"><?php echo esc_html($title); ?></h2>
                        <p class="kategori-text"><?php echo esc_html($short_text); ?></p>
                    </div>
                </a>
                <?php endwhile; ?>
            </div>
        </div>
    </div>
            <div class="pagination-wrapper">
                <div class="swiper-button-prev"></div>
                <div class="swiper-button-next"></div>
             </div>
<?php else : ?>
    <p>Inga kategorier hittades.</p>
<?php endif; ?>
