<?php
// Hämta alla platser från ACF repeater
$locations = get_field('map_locations');

if ($locations) : ?>
    <div style="max-width: 1200px; margin: auto; width: 90%; height: 700px; gap: 20px;" class="af-karta-div">
        <div id="map" style="width: 100%; height: 80%;"></div>

        <!-- Vänster kolumn: Lista med ÅF -->
        <div style="max-height: 700px; overflow-y: auto; background: var(--accentColor1); color: white;" class="div-1-af">
            <h3 style="font-size: 2em; margin-bottom: 10px; text-align: center; text-transform: uppercase; font-family: 'CustomHeadingFont';">Våra servicepoints</h3>

            <div style="margin-bottom: 20px; display: flex; align-items: center;">
                <input type="text" id="search-input" placeholder="Sök efter adress..."
                    style="color:#fff; width: 100%; padding: 12px; font-size: 1rem; border: none; background-color: var(--accentColor2);">
                <span style="cursor: pointer; font-size: 1.5rem; color: white;background-color: var(--accentColor); padding: 0rem;width: 40px;height: 40px;display: flex;justify-content: center;align-items: center;">
                    <i class="fas fa-search" style="font-size: 1rem; color: #000;"></i> <!-- Font Awesome sökikon -->
                </span>
            </div>

            <ul id="store-list" style="list-style: none; padding: 0;">
                <?php foreach ($locations as $index => $loc) : ?>
                    <li class="store-item" data-index="<?php echo $index; ?>"
                        style="font-size: 1.2rem; cursor: pointer; padding: 10px; background-color: var(--accentColor2); margin-bottom: 5px; display: flex; align-items: center; justify-content: space-between;">
                        <div>
                            <strong><?php echo esc_html($loc['location_name']); ?></strong><br>
                            <small><?php echo esc_html($loc['adress'] ?? ''); ?></small>
                        </div>
                        <!-- Chevron Icon -->
                        <span class="chevron-icon" style="margin-right: 10px;">
                            &#x276F; <!-- Unicode för en enkel chevron -->
                        </span>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>

        <!-- Höger kolumn: Karta -->
    </div>

    <script>
        function initMap() {
            const map = new google.maps.Map(document.getElementById('map'), {
                center: {
                    lat: 59.3293,
                    lng: 18.0686
                }, // Standard: Stockholm
                zoom: 5,
                styles: [{
                        "elementType": "geometry",
                        "stylers": [{
                            "color": "#f5f5f5"
                        }]
                    },
                    {
                        "elementType": "labels.icon",
                        "stylers": [{
                            "visibility": "off"
                        }]
                    },
                    {
                        "elementType": "labels.text.fill",
                        "stylers": [{
                            "color": "#616161"
                        }]
                    },
                    {
                        "elementType": "labels.text.stroke",
                        "stylers": [{
                            "color": "#f5f5f5"
                        }]
                    },
                    {
                        "featureType": "administrative.land_parcel",
                        "stylers": [{
                            "visibility": "off"
                        }]
                    },
                    {
                        "featureType": "administrative.neighborhood",
                        "stylers": [{
                            "visibility": "off"
                        }]
                    },
                    {
                        "featureType": "poi",
                        "stylers": [{
                            "visibility": "off"
                        }]
                    },
                    {
                        "featureType": "road",
                        "stylers": [{
                            "color": "#ffffff"
                        }]
                    },
                    {
                        "featureType": "road.arterial",
                        "stylers": [{
                            "color": "#ffffff"
                        }]
                    },
                    {
                        "featureType": "road.highway",
                        "stylers": [{
                            "color": "#ffffff"
                        }]
                    },
                    {
                        "featureType": "road.local",
                        "stylers": [{
                            "color": "#ffffff"
                        }]
                    },
                    {
                        "featureType": "transit",
                        "stylers": [{
                            "visibility": "off"
                        }]
                    },
                    {
                        "featureType": "water",
                        "stylers": [{
                            "color": "#b0b0b0"
                        }]
                    }
                ]
            });

            const locations = <?php echo json_encode($locations, JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_HEX_QUOT); ?>;
            const markers = [];
            const pinIcon = "<?php echo get_stylesheet_directory_uri(); ?>/assets/icons/map-pin.svg";

            locations.forEach((loc, index) => {
                const position = {
                    lat: parseFloat(loc.latitude),
                    lng: parseFloat(loc.longitude)
                };

                const marker = new google.maps.Marker({
                    position: position,
                    map: map,
                    icon: {
                        url: pinIcon,
                        scaledSize: new google.maps.Size(50, 50) // Mindre storlek för att särskilja pins
                    },
                    title: loc.location_name
                });

                markers.push({
                    marker,
                    position
                });

                const infoWindow = new google.maps.InfoWindow({
                    content: `<b>${loc.location_name}</b><br>${loc.adress || ''}`
                });

                marker.addListener("mouseover", () => infoWindow.open(map, marker));
                marker.addListener("mouseout", () => infoWindow.close());
            });

            // Klick på lista => centrera karta
            document.querySelectorAll(".store-item").forEach((item, index) => {
                item.addEventListener("click", function() {
                    const {
                        position
                    } = markers[index];
                    map.setCenter(position);
                    map.setZoom(7);
                });
            });

        }

        document.addEventListener("DOMContentLoaded", function() {
            const searchInput = document.getElementById("search-input");
            const storeItems = document.querySelectorAll(".store-item");

            searchInput.addEventListener("input", function() {
                const query = searchInput.value.toLowerCase();

                storeItems.forEach(item => {
                    const locationName = item.querySelector("strong").textContent.toLowerCase();
                    const address = item.querySelector("small").textContent.toLowerCase();

                    if (locationName.includes(query) || address.includes(query)) {
                        item.style.display = "flex"; // Visa matchande objekt
                    } else {
                        item.style.display = "none"; // Dölj icke-matchande objekt
                    }
                });
            });
        });
    </script>

    <script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCuwCkZoXo7yvtN2P2ukXYtJRNHeWyIq2I&callback=initMap"></script>

<?php endif; ?>