.cta-hover-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  width: 100%;
  background-color: transparent;
  overflow: hidden;
  gap: 30px;
}

@media screen and (max-width: 768px) {
  .cta-hover-wrapper {
    flex-direction: column;
    gap: 0px;
  }
}

.cta-hover-left {
  flex: 0 0 40%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* centrera vertikalt */
  color: white;
  gap: 20px;
}

.cta-hover-left .section-label {
  font-size: 16px;
  letter-spacing: 1px;

  text-transform: uppercase;
}

.section-label span {
  color: var(--accentColor);
}

.cta-link {
  color: white;
  font-size: 3rem;
  font-weight: bold;
  text-decoration: none;
  position: relative;
  transition: color 0.2s ease-in-out;
  text-transform: uppercase;
  padding: 10px 0px;
  border-bottom: 1px solid #403f3f;
  font-family: "CustomHeadingFont";
  cursor: none; /* Dölj standardmarkören när vi använder vår egen */
}

.cta-link:hover {
  color: var(--accentColor, #cde301);
}

.cta-hover-right {
  flex: 0 0 60%;
  position: relative;
  height: 100%;
  min-height: 700px;
  overflow: hidden;
}

@media screen and (max-width: 768px) {
  .cta-hover-right {
    flex: 0 0 100%;
    min-height: 400px;
    margin-top: 1rem;
  }
}

.cta-hover-right img.hover-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.4s ease-in-out;
  z-index: 0;
}

.cta-hover-right img.hover-img.active {
  opacity: 1;
  z-index: 1;
}

img.cta-arrow {
  position: absolute;
  right: 3%;
  top: 30%;
}

.desktop-only {
  display: flex;
}

.mobile-only {
  display: none;
}

@media screen and (max-width: 768px) {
  .desktop-only {
    display: none;
  }

  .mobile-only {
    display: block;
  }

  .cta-link {
    font-size: 1rem;
    width: 100%;
    display: flex;

    align-items: center;
    margin-left: 1rem;
  }

  .cta-container {
    display: flex;
  }

  .cta-container .hover-img {
    height: 100px;
    width: 100px;
  }
}

.cta-custom-cursor {
  position: fixed;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--accentColor, #cde301);
  pointer-events: none;
  mix-blend-mode: difference;
  transform: translate(-50%, -50%) scale(0);
  z-index: 9999;
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.cta-custom-cursor.active {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

@media screen and (max-width: 768px) {
  .cta-custom-cursor {
    display: none;
  }
}
