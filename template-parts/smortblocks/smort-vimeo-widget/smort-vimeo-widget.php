<?php

/**
 * Smort Vimeo Widget Block Template
 * 
 * @param array $block The block settings and attributes.
 * @param string $content The block inner HTML (empty).
 * @param bool $is_preview True during AJAX preview.
 * @param int|string $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'smort-vimeo-widget-' . $block['id'];
if (!empty($block['anchor'])) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$prefix = 'smort-vimeo-widget';

// Load values and assign defaults.
$video_id = !empty(get_field('video_id')) ? get_field('video_id') : null;

function render_vimeo_player($video_id)
{
    // Validate video ID
    if (!$video_id || !is_string($video_id)) {
        return;
    }

    // Set player parameters
    $params = array(
        'controls' => 1,    // Show playback controls
        'autoplay' => 0,    // Do not autoplay
        'loop' => 0,        // Don't loop the video
        'title' => 0,       // Hide video title
        'byline' => 0,      // Hide creator byline
        'portrait' => 0     // Hide portrait
    );

    // Construct iframe URL
    $base_url = 'https://player.vimeo.com/video/';
    $src = $base_url . $video_id;

    // Add parameters to URL
    $param_str = http_build_query($params);
    $src .= '?' . $param_str;

    // Create iframe markup
    $iframe = '<div class="vimeo-player">';
    $iframe .= '<iframe';
    $iframe .= ' src="' . esc_url($src) . '"';
    $iframe .= ' width="100%"';
    $iframe .= ' height="auto"';
    $iframe .= ' frameborder="0"';
    $iframe .= ' allowfullscreen';
    $iframe .= ' mozallowfullscreen';
    $iframe .= ' webkitallowfullscreen';
    $iframe .= '></iframe>';
    $iframe .= '</div>';

    echo $iframe;
}

?>

<section class="<?= $prefix; ?>" id="<?= $id; ?>">
    <?php
    render_vimeo_player($video_id);
    ?>
</section>