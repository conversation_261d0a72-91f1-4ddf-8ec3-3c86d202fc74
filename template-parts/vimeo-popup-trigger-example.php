<?php
/**
 * Example Vimeo Popup Trigger Button
 * 
 * This is an example of how to create a button that triggers the Vimeo popup.
 * You can copy this code and place it anywhere in your theme where you want
 * the popup trigger button to appear.
 * 
 * Usage:
 * 1. Copy the button HTML below
 * 2. Paste it anywhere in your theme templates
 * 3. Customize the button text and styling as needed
 * 4. Make sure the button has the class "vimeo-popup-trigger"
 */
?>

<!-- Example 1: Simple button -->
<button class="vimeo-popup-trigger">
    Watch Video
</button>

<!-- Example 2: Styled link button -->
<a href="#" class="vimeo-popup-trigger">
    <i class="fas fa-play"></i> Play Video
</a>

<!-- Example 3: But<PERSON> with custom styling -->
<button class="vimeo-popup-trigger custom-video-btn">
    <span class="btn-icon">▶</span>
    <span class="btn-text">Watch Our Story</span>
</button>

<!-- Example 4: Image with popup trigger -->
<div class="video-thumbnail" style="position: relative; display: inline-block;">
    <img src="your-video-thumbnail.jpg" alt="Video Thumbnail" style="width: 300px; height: 200px; object-fit: cover;">
    <button class="vimeo-popup-trigger" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.7); color: white; border: none; border-radius: 50%; width: 60px; height: 60px; font-size: 20px; cursor: pointer;">
        ▶
    </button>
</div>

<style>
/* Example custom styles for the video button */
.custom-video-btn {
    background: linear-gradient(45deg, #1ab7ea, #0ea5d3);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(26, 183, 234, 0.3);
}

.custom-video-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(26, 183, 234, 0.4);
}

.btn-icon {
    font-size: 18px;
}
</style>
