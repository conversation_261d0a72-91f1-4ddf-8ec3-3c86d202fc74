<?php

/**
 * ACF Options for Vimeo Popup
 */

if (function_exists('acf_add_local_field_group')) {
    acf_add_local_field_group(array(
        'key' => 'group_vimeo_popup_options',
        'title' => 'Vimeo Popup Settings',
        'fields' => array(
            array(
                'key' => 'field_popup_vimeo_video_id',
                'label' => 'Popup Vimeo Video ID',
                'name' => 'popup_vimeo_video_id',
                'type' => 'text',
                'instructions' => 'Enter the Vimeo video ID that will be displayed in the popup. This video will be shown when buttons with the class "vimeo-popup-trigger" are clicked.',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => 'e.g. 123456789',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'options_page',
                    'operator' => '==',
                    'value' => 'acf-options',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => 'Settings for the Vimeo popup functionality',
    ));
}
