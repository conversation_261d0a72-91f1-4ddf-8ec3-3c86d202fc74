<?php
defined( 'ABSPATH' ) || exit;
get_header( 'shop' );

// Kontrollera om vi är på en produktkategori eller shop-huvudsidan
$is_shop_main_page = is_shop();
$header_title = $is_shop_main_page ? "Alla våra produkter" : single_term_title('', false);
$term = get_queried_object();

// Hämta varumärkets utvalda bild
$brand_image = $term ? get_field('brand_image', $term) : '';

// Hämta kategori-AFC-fält endast om vi är på en kategori
$category_text = $term ? get_field('kategoritext_hogstupp', $term) : '';

// Fallback-text om ACF-fältet är tomt
$fallback_text = "Förutom vårt breda sortiment av xenonprodukter har vi de senaste åren utökat vår katalog till att även inkludera LED-produkter, vilket ger våra kunder ännu fler alternativ när det kommer till fordonsbelysning. LED-lampor, kända för sin överlägsna energieffektivitet, långa livslängd och exceptionella ljuskvalitet, har snabbt blivit ett populärt val bland bilägare och yrkesverksamma inom bilindustrin.";

// Använd fallback-text om inget ACF-värde finns
$category_text = !empty($category_text) ? $category_text : $fallback_text;

$category_video = $term ? get_field('kategorivideo', $term) : '';
$thumbnail_id = $term ? get_term_meta($term->term_id, 'thumbnail_id', true) : '';

// Bestäm bild
if ($is_shop_main_page) {
    $category_image = '/wp-content/uploads/2025/01/drone-xk.jpg'; // Standardbild för /shop
} else {
    $category_image = !empty($brand_image) ? $brand_image : ($thumbnail_id ? wp_get_attachment_url($thumbnail_id) : '/wp-content/uploads/2025/01/drone-xk.jpg');
}
?>

<!-- Kategorirubrik och bild/video -->
<div class="category-header">
        <div class="category-header-text">
        <h1 class="category-title"><?php echo esc_html($header_title); ?></h1>
        <?php if ($category_text) : ?>
            <div class="category-intro-container">
                <div class="category-intro"><?php echo wp_kses_post($category_text); ?></div>
            </div>
        <?php endif; ?>
    </div>

    <div class="category-header-media">
        <div class="category-media-wrapper">
            <img src="<?php echo esc_url($category_image); ?>" alt="<?php echo esc_attr($header_title); ?>" class="category-image">
            <?php if ($category_video): ?>
                <button class="video-play-button" id="playVideo" data-video="<?php echo esc_js($category_video); ?>">▶</button>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Produktlista och filtreringssektion -->
<div class="shop-loop-outer">
    <div class="shop-loop-content-smort">
    
        <div class="filter-area">
            <h2 class="filter-title">Filtrera produkter</h2>
            <?php
                        do_action( 'woocommerce_before_shop_loop' );
            ?>
        </div>
        <?php
        if ( woocommerce_product_loop() ) {
            woocommerce_product_loop_start();

            if ( wc_get_loop_prop( 'total' ) ) {
                while ( have_posts() ) {
                    the_post();
                    wc_get_template_part( 'content', 'product' );
                }
            }

            woocommerce_product_loop_end();
            do_action( 'woocommerce_after_shop_loop' );
        } else {
            do_action( 'woocommerce_no_products_found' );
        }
        ?>
    </div>
</div>

<!-- Kategoribeskrivning (endast på kategorisidor) -->
<?php 
if (is_product_category()) {
    $term = get_queried_object(); // Hämta aktuell produktkategori
    $acf_description = get_field('kategoritext_langstner', 'product_cat_' . $term->term_id); // Hämta ACF-fältet

    if ($acf_description): ?>
        <div class="category-description-container">
            <div class="category-description">
                <?php echo wp_kses_post($acf_description); ?>
            </div>
            <div class="fade-overlay"></div>
            <button class="las-mer-btn" onclick="toggleCategoryDescription()">Läs mer</button>
        </div>
    <?php endif; 
} 
?>



<?php
do_action( 'woocommerce_after_main_content' );
get_footer( 'shop' );
?>

<!-- Video Popup -->
<div id="videoPopup" class="video-popup">
    <div class="video-popup-content">
        <span class="close-popup">&times;</span>
        <iframe id="videoIframe" width="100%" height="500" frameborder="0" allowfullscreen></iframe>
    </div>
</div>




<script>
document.addEventListener("DOMContentLoaded", function () {
    const playButton = document.getElementById("playVideo");
    const videoPopup = document.getElementById("videoPopup");
    const videoIframe = document.getElementById("videoIframe");
    const closePopup = document.querySelector(".close-popup");

    if (playButton) {
        playButton.addEventListener("click", function () {
            const videoId = playButton.getAttribute("data-video");
            videoIframe.src = `https://www.youtube.com/embed/${videoId}?autoplay=1`;
            videoPopup.style.display = "flex";
        });
    }

    closePopup.addEventListener("click", function () {
        videoPopup.style.display = "none";
        videoIframe.src = "";
    });

    videoPopup.addEventListener("click", function (e) {
        if (e.target === videoPopup) {
            videoPopup.style.display = "none";
            videoIframe.src = "";
        }
    });
});

document.addEventListener("DOMContentLoaded", function () {
    const categoryContainer = document.querySelector(".category-description-container");
    const categoryDescription = document.querySelector(".category-description");
    const fadeOverlay = document.querySelector(".fade-overlay");
    const readMoreButton = document.querySelector(".las-mer-btn");

    if (categoryDescription.scrollHeight > 400) {
        readMoreButton.style.display = "block"; // Visa knappen om texten är längre än 400px
    } else {
        fadeOverlay.style.display = "none"; // Dölj fade-effekten om texten inte är för lång
    }

    window.toggleCategoryDescription = function () {
        categoryContainer.classList.toggle("expanded");

        if (categoryContainer.classList.contains("expanded")) {
            categoryDescription.style.maxHeight = categoryDescription.scrollHeight + "px";
            readMoreButton.textContent = "Visa mindre";
        } else {
            categoryDescription.style.maxHeight = "400px";
            readMoreButton.textContent = "Läs mer";
        }
    };
});


</script>