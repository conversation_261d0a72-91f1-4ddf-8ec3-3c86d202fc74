<?php
defined('ABSPATH') || exit;

global $product;

$egenskaper = get_field('egenskaper', $product->get_id());

if (str_contains($egenskaper, '|') !== false) {
    $egenskaper = str_replace('|', '<span class="separator">|</span>', $egenskaper);
}

?>

<li <?php wc_product_class('custom-product-card', $product); ?>>
    <div class="product-card">
        <?php
        /**
         * Hook: woocommerce_before_shop_loop_item.
         *
         * @hooked woocommerce_template_loop_product_link_open - 10
         */
        do_action('woocommerce_before_shop_loop_item');

        /**
         * Hook: woocommerce_before_shop_loop_item_title.
         *
         * @hooked woocommerce_template_loop_product_thumbnail - 10
         */
        do_action('woocommerce_before_shop_loop_item_title');
        ?>

        <div class="product-attribute"><?= $egenskaper ?></div>


        <div class="product-info">

            <div class="title-stock-div">
                <?php
                /**
                 * Hook: woocommerce_shop_loop_item_title.
                 *
                 * @hooked woocommerce_template_loop_product_title - 10
                 */
                do_action('woocommerce_shop_loop_item_title');

                ?>


            </div>

            <?php
            /**
             * Hook: woocommerce_after_shop_loop_item_title.
             *
             * @hooked woocommerce_template_loop_price - 10
             */
            do_action('woocommerce_after_shop_loop_item_title');


            /**
             * Hook: woocommerce_after_shop_loop_item.
             *
             * @hooked woocommerce_template_loop_product_link_close - 5
             * @hooked woocommerce_template_loop_add_to_cart - 10
             */
            do_action('woocommerce_after_shop_loop_item');
            ?>


            <div class="product-excerpt">
                <?php
                $excerpt = get_the_excerpt();
                if ($excerpt) {
                    $short_excerpt = wp_trim_words($excerpt, 10, '..');
                    echo '<p>' . esc_html($short_excerpt) . '</p>';
                }
                ?>
            </div>

        </div>
    </div>
</li>