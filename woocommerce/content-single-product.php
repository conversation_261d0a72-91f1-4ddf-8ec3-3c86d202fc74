<?php

/**
 * Custom content-single-product.php
 */
defined('ABSPATH') || exit;

global $product;

do_action('woocommerce_before_single_product');


if (post_password_required()) {
    echo get_the_password_form();
    return;
}
?>

<div id="product-<?php the_ID(); ?>" <?php wc_product_class(); ?>>
    <div class="product-columns-wrapper fade-in">
        <!-- Left Column: Custom Image Gallery -->
        <div class="product-image-gallery">
            <?php
            $attachment_ids = $product->get_gallery_image_ids();
            $featured_image = get_post_thumbnail_id() ? wp_get_attachment_url(get_post_thumbnail_id()) : '';

            if ($featured_image || !empty($attachment_ids)): ?>
                <div class="product-slider">
                    <div class="main-image-wrapper">
                        <button class="slider-arrow prev">&#10094;</button>
                        <div class="main-image-container">
                            <!-- Huvudbild med Fancybox -->
                            <a id="main-image-link" data-fancybox="product-gallery" href="<?php echo esc_url($featured_image); ?>">
                                <img id="main-image" src="<?php echo esc_url($featured_image); ?>" alt="Produktbild">
                            </a>
                        </div>
                        <button class="slider-arrow next">&#10095;</button>
                    </div>

                    <!-- Thumbnails (byter bild men öppnar ej fancybox) -->
                    <div class="thumbnail-wrapper">
                        <?php if ($featured_image): ?>
                            <img class="thumbnail active" src="<?php echo esc_url($featured_image); ?>" data-full="<?php echo esc_url($featured_image); ?>">
                        <?php endif; ?>

                        <?php foreach ($attachment_ids as $attachment_id):
                            $thumb_url = wp_get_attachment_image_url($attachment_id, 'thumbnail');
                            $full_url = wp_get_attachment_url($attachment_id);
                        ?>
                            <img class="thumbnail" src="<?php echo esc_url($thumb_url); ?>" data-full="<?php echo esc_url($full_url); ?>">
                        <?php endforeach; ?>
                    </div>

                    <!-- Dolda fancybox-länkar (syns inte, men gör att fancybox kan bläddra mellan bilderna) -->
                    <div style="display: none;">
                        <?php foreach ($attachment_ids as $attachment_id):
                            $full_url = wp_get_attachment_url($attachment_id);
                        ?>
                            <a href="<?php echo esc_url($full_url); ?>" data-fancybox="product-gallery"></a>
                        <?php endforeach; ?>
                    </div>
                </div>


            <?php endif; ?>



        </div>

        <!-- Right Column: Product Summary -->
        <div class="product-summary">
            <?php do_action('woocommerce_single_product_summary'); ?>
        </div>
    </div>
    <div id="popup-overlay" class="popup-overlay">
        <div class="popup-content">
            <span id="close-popup-btn" class="close-popup">&times;</span>
            <h2>Produktförfrågan</h2>
            <?= do_shortcode('[contact-form-7 id="2176efe" title="Produkt förfrågan"]'); ?>
        </div>
    </div>
</div>

<!-- TAAAABS  -->
<div class="custom-tabs-wrapper fade-in" id="tabs-content">
    <h2 class="tabs-main-title">Information</h2>

    <?php
    // Kontrollera om innehåll finns för varje sektion
    $produktbeskrivning_innehall = !empty($product->get_description());
    $teknisk_specifikation_innehall = get_field('teknisk_specifikation');
    $video_innehall = get_field('video_url');
    $installationsguide_innehall = get_field('video_url_installationsguide', 'options');
    ?>

    <!-- Tabbar -->
    <div class="custom-tabs">
        <?php if ($produktbeskrivning_innehall): ?>
            <button class="tab-button active" data-tab="produktbeskrivning">Produktbeskrivning</button>
        <?php endif; ?>
        <?php if ($teknisk_specifikation_innehall): ?>
            <button class="tab-button" data-tab="teknisk-specifikation">Teknisk specifikation</button>
        <?php endif; ?>
        <?php if ($video_innehall): ?>
            <button class="tab-button" data-tab="video">Video</button>
        <?php endif; ?>
        <?php if ($installationsguide_innehall): ?>
            <button class="tab-button" data-tab="installationsguide">Installationsguide</button>
        <?php endif; ?>
    </div>

    <!-- Tab Innehåll -->
    <?php if ($produktbeskrivning_innehall): ?>
        <div class="tab-content active" id="produktbeskrivning">
            <div class="wysiwyg-content-container">
                <div class="wysiwyg-content">
                    <?php echo wp_kses_post($product->get_description()); ?>
                </div>
                <div class="fade-overlay"></div> <!-- Fade-effekt -->
            </div>
            <button class="las-mer-btn" onclick="toggleDescription()">Läs mer</button>
        </div>
    <?php endif; ?>


    <?php if ($teknisk_specifikation_innehall): ?>
        <div class="tab-content" id="teknisk-specifikation">
            <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>" target="_blank">
                <input type="hidden" name="action" value="generate_product_pdf_tcpdf">
                <input type="hidden" name="product_id" value="<?php echo get_the_ID(); ?>">
            </form>
            <div class="wysiwyg-content">
                <?php echo wp_kses_post($teknisk_specifikation_innehall); ?>
            </div>
        </div>
    <?php endif; ?>

    <?php
    $video_id = get_field('video_url'); // Använd bara video-ID, t.ex. "UFkgEaPFP5Q"

    if ($video_id):
        $thumbnail_url = "https://img.youtube.com/vi/$video_id/maxresdefault.jpg";
    ?>

        <div class="tab-content" id="video">
            <!-- Video Thumbnail med knapp -->
            <div class="video-thumbnail-container-sp" onclick="openVideoPopup('<?php echo esc_js($video_id); ?>')">
                <img src="<?php echo esc_url($thumbnail_url); ?>" alt="Video Thumbnail" class="video-thumbnail">
                <button class="video-play-button">▶ Se video</button>
            </div>
        </div>

        <!-- Popup-overlay för videon -->
        <div id="video-popup" class="video-popup">
            <div class="video-popup-content">
                <span class="close-popup" onclick="closeVideoPopup()">&times;</span>
                <iframe id="video-iframe" width="100%" height="500" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    <?php endif; ?>

    <?php
    $video_id_installationsguide = get_field('video_url_installationsguide', 'option'); // Använd bara video-ID, t.ex. "UFkgEaPFP5Q"

    if ($video_id_installationsguide):
        $thumbnail_url_installationsguide = "https://img.youtube.com/vi/$video_id_installationsguide/maxresdefault.jpg";
    ?>
        <div class="tab-content" id="installationsguide">
            <!-- Video Thumbnail med knapp -->
            <div class="video-thumbnail-container-sp" onclick="openVideoPopup('<?php echo esc_js($video_id_installationsguide); ?>')">
                <img src="<?php echo esc_url($thumbnail_url_installationsguide); ?>" alt="Video Thumbnail" class="video-thumbnail">
                <button class="video-play-button">▶ Se video</button>
            </div>
        </div>

        <!-- Popup-overlay för videon -->
        <div id="video-popup" class="video-popup">
            <div class="video-popup-content">
                <span class="close-popup" onclick="closeVideoPopup()">&times;</span>
                <iframe id="video-iframe" width="100%" height="500" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    <?php endif; ?>


</div>



<!---- HOTSPOTS ------>

<?php
// Hämta ACF-fält för båda bilderna
$hotspot_bild_1 = get_field('hotspot_bild_1');
$hotspot_punkter_1 = get_field('hotspot_punkter_1');

$hotspot_bild_2 = get_field('hotspot_bild_2');
$hotspot_punkter_2 = get_field('hotspot_punkter_2');

// Kontrollera att minst en bild finns
if ($hotspot_bild_1 || $hotspot_bild_2): ?>
    <div class="hotspot-section-container fade-in">
        <?php if ($hotspot_bild_1): ?>
            <div class="hotspot-container half-width">
                <img src="<?php echo esc_url($hotspot_bild_1); ?>" alt="Hotspot Bild 1" style="width: 100%; height: auto;">
                <?php if ($hotspot_punkter_1): ?>
                    <?php foreach ($hotspot_punkter_1 as $punkt): ?>
                        <div class="hotspot" style="left: <?php echo esc_attr($punkt['hotspot_x']); ?>%; top: <?php echo esc_attr($punkt['hotspot_y']); ?>%;">
                            <div class="hotspot-dot"></div>
                            <div class="hotspot-info"><?php echo esc_html($punkt['hotspot_info']); ?></div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?php if ($hotspot_bild_2): ?>
            <div class="hotspot-container half-width">
                <img src="<?php echo esc_url($hotspot_bild_2); ?>" alt="Hotspot Bild 2" style="width: 100%; height: auto;">
                <?php if ($hotspot_punkter_2): ?>
                    <?php foreach ($hotspot_punkter_2 as $punkt): ?>
                        <div class="hotspot" style="left: <?php echo esc_attr($punkt['hotspot_x']); ?>%; top: <?php echo esc_attr($punkt['hotspot_y']); ?>%;">
                            <div class="hotspot-dot"></div>
                            <div class="hotspot-info"><?php echo esc_html($punkt['hotspot_info']); ?></div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>



<!---- Produktbild slider ----->

<?php
// Hämta repeaterfältet för slidern
$bilder = get_field('produkt_bild_slider');

if ($bilder): ?>
    <div class="produkt-slider-container fade-in">
        <div class="produkt-slider">
            <?php foreach ($bilder as $index => $bild): ?>
                <div class="produkt-slide <?php echo $index === 0 ? 'active' : ''; ?>">
                    <img src="<?php echo esc_url($bild['slider_bild']); ?>" alt="Produktbild <?php echo $index + 1; ?>">
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Navigationspilar -->
        <button class="slider-nav prev">&larr;</button>
        <button class="slider-nav next">&rarr;</button>

        <!-- Bildindikator -->
        <div class="slider-indicator">
            <div class="indicator-progress"></div>
        </div>
    </div>
<?php endif; ?>

<!---- Relaterade produkter ----->

<?php
global $product;

$terms = get_the_terms($product->get_id(), 'product_cat');

if ($terms && !is_wp_error($terms)) {
    $category_ids = wp_list_pluck($terms, 'term_id');

    $args = array(
        'post_type'      => 'product',
        'posts_per_page' => 10,
        'post__not_in'   => array($product->get_id()),
        'tax_query'      => array(
            array(
                'taxonomy' => 'product_cat',
                'field'    => 'term_id',
                'terms'    => $category_ids,
            ),
        ),
    );

    $related_query = new WP_Query($args);

    if ($related_query->have_posts()) : ?>

        <div class="related-slider-container fade-in">
            <h2 class="related-slider-maintitle"> Relaterade produkter</h2>
            <div class="swiper-container related-carousel woocommerce">
                <div class="swiper-wrapper">
                    <?php while ($related_query->have_posts()) : $related_query->the_post(); ?>
                        <div class="swiper-slide related-slide">
                            <a href="<?php the_permalink(); ?>">
                                <?php woocommerce_show_product_sale_flash(); ?>

                                <?php
                                if (has_post_thumbnail()) {
                                    the_post_thumbnail('full');
                                } else {

                                    echo '<img class="placeholder" src="' . wc_placeholder_img_src() . '" alt="Placeholder" />';
                                }
                                ?>
                                <div class="slider-content-container">
                                    <h2 class="related-slider-title"><?php the_title(); ?></h2>
                                    <p class="related-slider-text">
                                    <div class="product-excerpt">
                                        <?php
                                        $excerpt = get_the_excerpt();
                                        if ($excerpt) {
                                            $short_excerpt = wp_trim_words($excerpt, 10, '..');
                                            echo '<p>' . esc_html($short_excerpt) . '</p>';
                                        }
                                        ?>
                                    </div>

                                    </p>
                                </div>
                            </a>
                        </div>
                    <?php endwhile; ?>
                </div>
            </div>
    <?php endif;

    wp_reset_postdata();
}
    ?>
        </div>

        <script>
            document.addEventListener("DOMContentLoaded", function() {
                new Swiper('.swiper-container', {
                    slidesPerView: 4,
                    loop: true,
                    navigation: {
                        nextEl: '.related-next',
                        prevEl: '.related-prev',
                    },
                    slidesPerView: 1,
                    spaceBetween: 10,
                    breakpoints: {
                        0: {
                            // For screens smaller than 480px
                            slidesPerView: 1.1, // Show 1 slide
                        },
                        800: {
                            // For screens between 481px and 768px
                            slidesPerView: 2.5, // Show 2.5 slides
                        },
                        1500: {
                            // For screens larger than 768px
                            slidesPerView: 3.5, // Show 3.5 slides
                        },
                        2000: {
                            // For screens larger than 768px
                            slidesPerView: 4.5, // Show 3.5 slides
                        },
                    },
                });
            });

            document.addEventListener('DOMContentLoaded', () => {
                const addons = document.querySelectorAll('.yith-wapo-addon');

                addons.forEach(addon => {
                    const options = addon.querySelectorAll('.yith-wapo-option');
                    if (options.length === 1) {
                        addon.style.display = 'none';
                    }
                });
            });


            document.addEventListener('DOMContentLoaded', function() {
                // Loopar igenom alla tabbsektioner
                document.querySelectorAll('.custom-tabs-wrapper').forEach((wrapper) => {
                    const tabs = wrapper.querySelectorAll('.tab-button');
                    const contents = wrapper.querySelectorAll('.tab-content');

                    tabs.forEach(tab => {
                        tab.addEventListener('click', () => {
                            // Ta bort "active" endast inom den aktuella tabbgruppen
                            tabs.forEach(t => t.classList.remove('active'));
                            contents.forEach(c => c.classList.remove('active'));

                            // Lägg till "active" på den valda tabben och innehållet
                            tab.classList.add('active');
                            const target = wrapper.querySelector(`#${tab.getAttribute('data-tab')}`);
                            if (target) {
                                target.classList.add('active');
                            }
                        });
                    });
                });
            });


            /* Video player */

            function openVideoPopup(videoId) {
                const videoIframe = document.getElementById("video-iframe");
                const videoPopup = document.getElementById("video-popup");

                videoIframe.src = "https://www.youtube.com/embed/" + videoId + "?autoplay=1";
                videoPopup.classList.add("active");
            }

            function closeVideoPopup() {
                const videoPopup = document.getElementById("video-popup");
                const videoIframe = document.getElementById("video-iframe");

                videoPopup.classList.remove("active");
                videoIframe.src = ""; // Stoppar videon när popupen stängs
            }

            document.addEventListener("DOMContentLoaded", function() {
                const contentContainer = document.querySelector(".wysiwyg-content-container");
                const content = document.querySelector(".wysiwyg-content");
                const readMoreButton = document.querySelector(".las-mer-btn");

                // Kontrollera om texten är längre än 250px
                if (content.scrollHeight > 450) {
                    contentContainer.classList.add("overflow"); // Lägg till klass för att visa knappen
                    readMoreButton.style.display = "block"; // Visa knappen
                }

                // Funktion för att expandera eller kollapsa texten
                window.toggleDescription = function() {
                    contentContainer.classList.toggle("expanded");
                    if (contentContainer.classList.contains("expanded")) {
                        contentContainer.style.maxHeight = content.scrollHeight + "px";
                        readMoreButton.textContent = "Visa mindre";
                    } else {
                        contentContainer.style.maxHeight = "450px";
                        readMoreButton.textContent = "Läs mer";
                    }
                };
            });



            document.addEventListener('DOMContentLoaded', function() {
                const slides = document.querySelectorAll('.produkt-slide');
                const prevButton = document.querySelector('.slider-nav.prev');
                const nextButton = document.querySelector('.slider-nav.next');
                const progressIndicator = document.querySelector('.indicator-progress');

                let currentIndex = 0;

                function updateSlider() {
                    const slider = document.querySelector('.produkt-slider');
                    slider.style.transform = `translateX(-${currentIndex * 100}%)`;
                    progressIndicator.style.width = `${((currentIndex + 1) / slides.length) * 100}%`;
                }

                prevButton.addEventListener('click', () => {
                    currentIndex = (currentIndex === 0) ? slides.length - 1 : currentIndex - 1;
                    updateSlider();
                });

                nextButton.addEventListener('click', () => {
                    currentIndex = (currentIndex === slides.length - 1) ? 0 : currentIndex + 1;
                    updateSlider();
                });

                // Initiera indikator
                updateSlider();
            });



            document.addEventListener('DOMContentLoaded', () => {
                const sections = document.querySelectorAll('.fade-in');

                // Funktion som kollar om en sektion är synlig
                const isVisible = (element) => {
                    const rect = element.getBoundingClientRect();
                    return (
                        rect.top <= window.innerHeight && rect.bottom >= 0
                    );
                };

                const handleScroll = () => {
                    sections.forEach((section) => {
                        if (isVisible(section)) {
                            section.classList.add('show');
                        } else {
                            section.classList.remove('show'); // Ta bort show för att animera igen
                        }
                    });
                };

                // Kör funktionen på scroll och vid laddning
                window.addEventListener('scroll', handleScroll);
                handleScroll(); // Kör en gång vid sidladdning
            });


            // BILDGALLERI PRODUKTBILDER 
            document.addEventListener('DOMContentLoaded', function() {
                const thumbnails = document.querySelectorAll('.thumbnail');
                const mainImage = document.getElementById('main-image');
                const mainImageLink = document.getElementById('main-image-link');
                const prevBtn = document.querySelector('.slider-arrow.prev');
                const nextBtn = document.querySelector('.slider-arrow.next');

                let currentIndex = 0;

                function updateMainImage(index) {
                    const newSrc = thumbnails[index].getAttribute('data-full');
                    mainImage.setAttribute('src', newSrc);
                    mainImageLink.setAttribute('href', newSrc);
                    thumbnails.forEach(t => t.classList.remove('active'));
                    thumbnails[index].classList.add('active');
                    currentIndex = index;
                }

                thumbnails.forEach((thumb, index) => {
                    thumb.addEventListener('click', () => {
                        updateMainImage(index);
                    });
                });

                prevBtn.addEventListener('click', () => {
                    let newIndex = (currentIndex - 1 + thumbnails.length) % thumbnails.length;
                    updateMainImage(newIndex);
                });

                nextBtn.addEventListener('click', () => {
                    let newIndex = (currentIndex + 1) % thumbnails.length;
                    updateMainImage(newIndex);
                });

                // Initiera
                updateMainImage(currentIndex);
            });



            // Bildgalleri längre ned 


            document.addEventListener('DOMContentLoaded', function() {
                const sliderContainer = document.querySelector('.produkt-slider-container');
                const slides = sliderContainer.querySelectorAll('.produkt-slide');
                const prevButton = sliderContainer.querySelector('.slider-nav.prev');
                const nextButton = sliderContainer.querySelector('.slider-nav.next');
                const progressIndicator = sliderContainer.querySelector('.indicator-progress');
                let currentIndex = 0;
                const totalSlides = slides.length;

                // Funktion för att uppdatera slidern
                function updateSlider() {
                    const slider = sliderContainer.querySelector('.produkt-slider');
                    slider.style.transform = `translateX(-${currentIndex * 100}%)`;

                    // Uppdatera indikatorns bredd
                    const progressWidth = ((currentIndex + 1) / totalSlides) * 100;
                    progressIndicator.style.width = `${progressWidth}%`;

                    // Uppdatera aktiva klass
                    slides.forEach((slide, index) => {
                        slide.classList.toggle('active', index === currentIndex);
                    });
                }

                // Hantera "Föregående"-knappen
                prevButton.addEventListener('click', () => {
                    currentIndex = (currentIndex === 0) ? totalSlides - 1 : currentIndex - 1;
                    updateSlider();
                });

                // Hantera "Nästa"-knappen
                nextButton.addEventListener('click', () => {
                    currentIndex = (currentIndex + 1) % totalSlides;
                    updateSlider();
                });

                // Initialisera slidern
                updateSlider();
            });



            // Bildgalleri längre ned
            document.addEventListener('DOMContentLoaded', function() {
                const sliderContainer = document.querySelector('.produkt-slider-container');
                const slides = sliderContainer.querySelectorAll('.produkt-slide');
                const prevButton = sliderContainer.querySelector('.slider-nav.prev');
                const nextButton = sliderContainer.querySelector('.slider-nav.next');
                const progressIndicator = sliderContainer.querySelector('.indicator-progress');
                let currentIndex = 0;
                const totalSlides = slides.length;

                // Funktion för att uppdatera slidern
                function updateSlider() {
                    const slider = sliderContainer.querySelector('.produkt-slider');
                    slider.style.transform = `translateX(-${currentIndex * 100}%)`;

                    // Uppdatera indikatorns bredd
                    const progressWidth = ((currentIndex + 1) / totalSlides) * 100;
                    progressIndicator.style.width = `${progressWidth}%`;

                    // Uppdatera aktiva klass
                    slides.forEach((slide, index) => {
                        slide.classList.toggle('active', index === currentIndex);
                    });
                }

                // Hantera "Föregående"-knappen
                prevButton.addEventListener('click', () => {
                    currentIndex = (currentIndex === 0) ? totalSlides - 1 : currentIndex - 1;
                    updateSlider();
                });

                // Hantera "Nästa"-knappen
                nextButton.addEventListener('click', () => {
                    currentIndex = (currentIndex + 1) % totalSlides;
                    updateSlider();
                });

                // Initialisera slidern
                updateSlider();
            });
        </script>


        <style>
            .product-slider {
                max-width: 100%;
            }

            .main-image-wrapper {
                position: relative;
                text-align: center;
            }

            .main-image-container {
                width: 100%;
                height: auto;
                display: flex;
                align-items: center;
                justify-content: center;
                overflow: hidden;
                height: 700px;
                background-color: #fff;
                ;
            }

            .main-image-container img {
                max-width: 100%;
                max-height: 700px;
                object-fit: contain;
                /* eller 'cover' om du hellre fyller hela rutan */
                transition: opacity 0.3s ease-in-out;
            }


            .slider-arrow {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                background: transparent;
                border: none;
                color: var(--accentColor);
                font-size: 24px;
                padding: 10px;
                cursor: pointer;
                z-index: 2;
            }

            .slider-arrow.prev {
                left: 10px;
            }

            .slider-arrow.next {
                right: 10px;
            }

            .thumbnail-wrapper {
                display: flex;
                gap: 10px;
                margin-top: 10px;
                justify-content: center;
                flex-wrap: wrap;
            }

            .thumbnail {
                width: 100px;
                height: 100px;
                object-fit: cover;
                border: 2px solid transparent;
                cursor: pointer;
                transition: border 0.3s;
            }

            .thumbnail.active {
                border: 2px solid var(--accentColor);
            }

            @media screen and (max-width: 992px) {
                .thumbnail {
                    width: 40px;
                    height: 40px;
                    object-fit: cover;
                    border: 2px solid transparent;
                    cursor: pointer;
                    transition: border 0.3s;
                }

                .main-image-container {
                    height: 400px;
                }
            }
        </style>