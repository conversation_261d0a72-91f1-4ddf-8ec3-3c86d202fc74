<?php
/**
 * The Template for displaying all single products
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @package WooCommerce\Templates
 */

defined( 'ABSPATH' ) || exit;

get_header( 'shop' );

/**
 * woocommerce_before_main_content hook.
 *
 * @hooked woocommerce_output_content_wrapper - 10 (outputs opening divs for the content)
 * @hooked woocommerce_breadcrumb - 20
 */
do_action( 'woocommerce_before_main_content' );



while ( have_posts() ) :
    the_post();

    global $product;

    // Kontrollera om produkten tillhör kategorin "Cyklar"
    if ( has_term( 'cyklar', 'product_cat', $product->get_id() ) ) {
        // Använd den specifika mallen för cyklar
        wc_get_template_part( 'content', 'single-product-cyklar' );
    } else {
        // Använd standardmallen för andra produkter
        wc_get_template_part( 'content', 'single-product' );
    }

endwhile; // end of the loop.

get_footer( 'shop' );
