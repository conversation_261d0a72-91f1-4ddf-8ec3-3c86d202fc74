/**
 * Vimeo Popup Modal Styles
 */

.vimeo-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.vimeo-popup-overlay[style*="flex"] {
    opacity: 1;
    visibility: visible;
}

.vimeo-popup-content {
    position: relative;
    width: 90%;
    max-width: 800px;
    height: 60vh;
    max-height: 450px;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.vimeo-popup-overlay[style*="flex"] .vimeo-popup-content {
    transform: scale(1);
}

.vimeo-popup-close {
    position: absolute;
    top: -40px;
    right: 0;
    background: none;
    border: none;
    color: white;
    font-size: 30px;
    cursor: pointer;
    z-index: 10001;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.vimeo-popup-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.vimeo-popup-player {
    width: 100%;
    height: 100%;
    position: relative;
}

.vimeo-popup-player iframe {
    width: 100%;
    height: 100%;
    border: 0;
}

/* Trigger button styles */
.vimeo-popup-trigger {
    display: inline-block;
    padding: 12px 24px;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.vimeo-popup-trigger:hover {
    transform: translateY(-2px);
    text-decoration: none;
    color: white;
}

.vimeo-popup-trigger:focus {
    outline-offset: 2px;
}

/* Responsive styles */
@media (max-width: 768px) {
    .vimeo-popup-content {
        width: 95%;
        height: 50vh;
        max-height: 300px;
    }
    
    .vimeo-popup-close {
        top: -35px;
        font-size: 24px;
        width: 35px;
        height: 35px;
    }
}

@media (max-width: 480px) {
    .vimeo-popup-content {
        width: 98%;
        height: 45vh;
        max-height: 250px;
        border-radius: 4px;
    }
    
    .vimeo-popup-close {
        top: -30px;
        font-size: 20px;
        width: 30px;
        height: 30px;
    }
}

/* Animation for smooth opening */
@keyframes vimeoPopupFadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.vimeo-popup-overlay[style*="flex"] .vimeo-popup-content {
    animation: vimeoPopupFadeIn 0.3s ease-out;
}
